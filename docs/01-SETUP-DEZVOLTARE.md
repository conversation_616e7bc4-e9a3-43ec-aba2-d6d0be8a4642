# 🛠️ SETUP MEDIU DEZVOLTARE

## 📋 CERINȚE DE SISTEM

### Software Necesar
- **Node.js**: 18.0.0+ (L<PERSON> recomandat)
- **npm**: 8.0.0+ (vine cu Node.js)
- **Git**: Pentru version control
- **PostgreSQL**: 13+ (pentru producție)
- **Redis**: 6+ (opțional, pentru cache)
- **Editor**: VS Code recomandat cu extensii

### Extensii VS Code Recomandate
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "prisma.prisma",
    "ms-vscode.vscode-json"
  ]
}
```

---

## 🚀 INSTALARE PAS CU PAS

### 1. Clonarea Proiectului
```bash
# Clonează repository-ul
git clone [repository-url]
cd expense-tracker

# Verifică structura
ls -la
# Ar trebui să vezi: backend/, frontend/, docs/
```

### 2. Setup Backend

#### Instalare Dependențe
```bash
cd backend
npm install

# Verifică instalarea
npm list --depth=0
```

#### Configurare Variabile de Mediu
```bash
# Copiază template-ul
cp .env.example .env

# Editează .env cu configurările tale
nano .env  # sau code .env
```

#### Configurare .env Backend
```env
# Server Configuration
PORT=3000
NODE_ENV=development
API_VERSION=1.0.0

# Database Configuration  
DATABASE_URL="file:./data/expense_tracker.db"
# Pentru PostgreSQL: "postgresql://user:password@localhost:5432/expense_tracker"

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
FRONTEND_URL=http://localhost:5173

# Stripe Configuration (opțional pentru început)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Redis Configuration (opțional)
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600
```

#### Inițializare Baza de Date
```bash
# Generează clientul Prisma
npm run db:generate

# Aplică migrările
npm run db:migrate

# Populează cu date de test
npm run db:seed
```

#### Pornire Backend
```bash
# Dezvoltare (cu hot reload)
npm run dev

# Verifică că funcționează
curl http://localhost:3000/api/health
```

### 3. Setup Frontend

#### Instalare Dependențe
```bash
cd ../frontend
npm install

# Verifică instalarea
npm list --depth=0
```

#### Configurare Variabile de Mediu
```bash
# Creează .env pentru frontend
touch .env

# Adaugă configurările
echo "REACT_APP_API_URL=http://localhost:3000/api" > .env
```

#### Pornire Frontend
```bash
# Dezvoltare (cu hot reload)
npm run dev

# Aplicația va fi disponibilă la:
# Local: http://localhost:5173
# Rețea: http://[IP-local]:5173
```

---

## 🔧 CONFIGURĂRI AVANSATE

### Configurare PostgreSQL (Producție)

#### Instalare PostgreSQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS cu Homebrew
brew install postgresql
brew services start postgresql

# Windows
# Descarcă de pe https://www.postgresql.org/download/windows/
```

#### Configurare Bază de Date
```bash
# Conectează-te la PostgreSQL
sudo -u postgres psql

# Creează baza de date și utilizatorul
CREATE DATABASE expense_tracker;
CREATE USER expense_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE expense_tracker TO expense_user;
\q
```

#### Actualizare .env pentru PostgreSQL
```env
DATABASE_URL="postgresql://expense_user:secure_password@localhost:5432/expense_tracker"
```

### Configurare Redis (Cache)

#### Instalare Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server

# macOS cu Homebrew
brew install redis
brew services start redis

# Windows
# Folosește Docker sau WSL
```

#### Testare Redis
```bash
# Testează conexiunea
redis-cli ping
# Ar trebui să returneze: PONG
```

---

## 🧪 VERIFICARE SETUP

### Checklist Verificare
```bash
# 1. Verifică versiunile
node --version    # >= 18.0.0
npm --version     # >= 8.0.0
git --version

# 2. Testează backend
cd backend
npm test
curl http://localhost:3000/api/health

# 3. Testează frontend  
cd ../frontend
npm test
# Verifică în browser: http://localhost:5173

# 4. Testează accesul din rețea
# Găsește IP-ul local
ipconfig  # Windows
ifconfig  # Linux/macOS

# Testează din alt dispozitiv
curl http://[IP-local]:3000/api/health
# Browser: http://[IP-local]:5173
```

### Teste Funcționale
1. **Autentificare**: Încearcă <NAME_EMAIL> / admin123
2. **CRUD Cheltuieli**: Adaugă, editează, șterge o cheltuială
3. **Categorii**: Testează gestionarea categoriilor
4. **Export**: Testează funcționalitatea de export
5. **Responsive**: Testează pe mobile și desktop

---

## 🔍 DEBUGGING ȘI TROUBLESHOOTING

### Probleme Comune

#### Port-uri Ocupate
```bash
# Verifică ce folosește portul
netstat -tulpn | grep :3000  # Linux/macOS
netstat -ano | findstr :3000  # Windows

# Schimbă porturile în .env
PORT=3001  # backend
# Frontend: modifică în package.json sau webpack.config.js
```

#### Erori de Dependențe
```bash
# Șterge node_modules și reinstalează
rm -rf node_modules package-lock.json
npm install

# Verifică compatibilitatea Node.js
npm ls
```

#### Probleme Bază de Date
```bash
# Resetează baza de date
npm run db:reset

# Verifică conexiunea
npm run db:studio  # Deschide Prisma Studio
```

#### Probleme de Build
```bash
# Curăță cache-ul
npm run clean
npm run build

# Verifică erorile TypeScript
npm run type-check
```

### Log-uri și Monitoring

#### Backend Logs
```bash
# Logs în timp real
tail -f logs/app.log
tail -f logs/error.log

# Logs structurate
npm run dev | bunyan  # dacă ai bunyan instalat
```

#### Frontend Debugging
- **Chrome DevTools**: F12 → Console/Network/Sources
- **React DevTools**: Extensie browser pentru React
- **Redux DevTools**: Pentru state management

---

## 🚀 OPTIMIZĂRI DEZVOLTARE

### Hot Reload și Watch Mode
```bash
# Backend cu nodemon
npm run dev

# Frontend cu Webpack Dev Server
npm run dev

# TypeScript compilation în watch mode
npm run dev:build  # backend
npm run type-check:watch  # frontend
```

### Configurare IDE

#### VS Code Settings
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  }
}
```

#### Snippets Utile
```json
{
  "React Functional Component": {
    "prefix": "rfc",
    "body": [
      "import React from 'react';",
      "",
      "const ${1:ComponentName} = () => {",
      "  return (",
      "    <div>",
      "      $0",
      "    </div>",
      "  );",
      "};",
      "",
      "export default ${1:ComponentName};"
    ]
  }
}
```

---

## 📊 METRICI ȘI PERFORMANȚĂ

### Benchmark-uri Țintă
- **Backend startup**: < 3 secunde
- **Frontend build**: < 30 secunde  
- **Hot reload**: < 1 secundă
- **API response**: < 200ms (95th percentile)
- **Page load**: < 2 secunde

### Monitoring Dezvoltare
```bash
# Măsoară timpul de build
time npm run build

# Analizează bundle size
npm run analyze  # dacă ai webpack-bundle-analyzer

# Monitorizează memoria
node --inspect src/app.js
# Apoi deschide chrome://inspect
```

---

## 🔄 WORKFLOW DEZVOLTARE

### Git Workflow
```bash
# Creează branch pentru feature
git checkout -b feature/nume-feature

# Commit-uri frecvente și mici
git add .
git commit -m "feat: adaugă funcționalitate X"

# Push și pull request
git push origin feature/nume-feature
```

### Pre-commit Hooks
```bash
# Instalează husky (dacă nu e deja)
npm install --save-dev husky

# Configurează pre-commit
npx husky add .husky/pre-commit "npm run lint && npm test"
```

### Code Review Checklist
- [ ] Codul compilează fără erori
- [ ] Testele trec
- [ ] ESLint fără erori
- [ ] Prettier aplicat
- [ ] Funcționalitatea testată manual
- [ ] Documentația actualizată

---

*Ultima actualizare: Ianuarie 2025*