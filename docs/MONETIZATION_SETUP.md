# Monetization Setup Guide

Acest ghid vă va ajuta să configurați funcționalitățile de monetizare cu Stripe pentru aplicația Expense Tracker.

## 📋 Cerințe preliminare

1. **Cont Stripe**: Creați un cont pe [Stripe Dashboard](https://dashboard.stripe.com)
2. **Node.js**: Versiunea 18+ instalată
3. **Baza de date**: PostgreSQL sau SQLite configurată

## 🚀 Configurare inițială

### 1. Instalarea dependențelor

```bash
npm install
```

### 2. Configurarea variabilelor de mediu

Copiați `.env.example` în `.env` și completați următoarele variabile:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_SUCCESS_URL=http://localhost:5173/subscription/success
STRIPE_CANCEL_URL=http://localhost:5173/subscription/cancel

# Subscription Plans (Stripe Price IDs)
STRIPE_BASIC_PLAN_ID=price_basic_plan_id_here
STRIPE_PREMIUM_PLAN_ID=price_premium_plan_id_here

# Plan Limits
FREE_PLAN_EXPENSE_LIMIT=50
BASIC_PLAN_EXPENSE_LIMIT=500
PREMIUM_PLAN_EXPENSE_LIMIT=unlimited
```

### 3. Configurarea planurilor în Stripe

1. Accesați [Stripe Dashboard > Products](https://dashboard.stripe.com/products)
2. Creați următoarele produse:

#### Plan Basic ($9.99/lună)
- Nume: "Basic Plan"
- Preț: $9.99 USD
- Interval: Lunar
- Copiați Price ID-ul în `STRIPE_BASIC_PLAN_ID`

#### Plan Premium ($19.99/lună)
- Nume: "Premium Plan"
- Preț: $19.99 USD
- Interval: Lunar
- Copiați Price ID-ul în `STRIPE_PREMIUM_PLAN_ID`

### 4. Migrarea bazei de date

```bash
npm run db:migrate
```

### 5. Inițializarea planurilor

```bash
npm run stripe:plans
```

### 6. Configurarea webhook-urilor

```bash
npm run stripe:webhooks:setup
```

Sau manual în [Stripe Dashboard > Webhooks](https://dashboard.stripe.com/webhooks):
- URL: `http://your-domain.com/api/webhooks/stripe`
- Evenimente: `customer.subscription.*`, `invoice.payment_*`, `checkout.session.*`

## 🏗️ Structura implementată

### Modele de date

- **Plan**: Definește planurile de abonament
- **Subscription**: Gestionează abonamentele utilizatorilor
- **UsageLog**: Urmărește utilizarea funcționalităților
- **WebhookEvent**: Înregistrează evenimentele Stripe

### Servicii

- **StripeService**: Integrare cu API-ul Stripe
- **SubscriptionService**: Gestionarea abonamentelor
- **UsageService**: Urmărirea utilizării

### Controlere

- **SubscriptionController**: API-uri pentru abonamente
- **WebhookController**: Procesarea evenimentelor Stripe

### Middleware

- **SubscriptionMiddleware**: Verificarea permisiunilor bazate pe plan

## 📊 Planuri disponibile

### Free Plan
- **Preț**: Gratuit
- **Limite**: 50 cheltuieli/lună
- **Funcționalități**: Tracking de bază

### Basic Plan ($9.99/lună)
- **Preț**: $9.99/lună
- **Limite**: 500 cheltuieli/lună
- **Funcționalități**: Export date, rapoarte de bază

### Premium Plan ($19.99/lună)
- **Preț**: $19.99/lună
- **Limite**: Nelimitat
- **Funcționalități**: Toate funcționalitățile, rapoarte avansate, suport prioritar, API access

## 🔗 API Endpoints

### Publice
- `GET /api/subscriptions/plans` - Lista planurilor
- `POST /api/webhooks/stripe` - Webhook Stripe

### Autentificate
- `GET /api/subscriptions/current` - Abonamentul curent
- `POST /api/subscriptions/checkout` - Creare sesiune checkout
- `POST /api/subscriptions/portal` - Portal gestionare abonament
- `POST /api/subscriptions/cancel` - Anulare abonament
- `POST /api/subscriptions/reactivate` - Reactivare abonament
- `GET /api/subscriptions/usage` - Statistici utilizare

### Admin
- `POST /api/subscriptions/sync-plans` - Sincronizare planuri
- `GET /api/subscriptions/stats` - Statistici abonamente

## 🛠️ Comenzi utile

```bash
# Configurare completă monetizare
npm run setup:monetization

# Gestionare planuri
npm run stripe:plans

# Gestionare webhooks
npm run stripe:webhooks:setup
npm run stripe:webhooks:list

# Dezvoltare
npm run dev
npm run db:studio
```

## 🔒 Securitate

1. **Validarea webhook-urilor**: Toate webhook-urile sunt validate cu semnătura Stripe
2. **Autentificare**: Toate endpoint-urile sunt protejate cu JWT
3. **Autorizare**: Verificarea permisiunilor bazate pe plan
4. **Rate limiting**: Limitarea cererilor în funcție de plan

## 🐛 Debugging

### Verificarea webhook-urilor
```bash
# Listarea webhook-urilor
npm run stripe:webhooks:list

# Testarea webhook-urilor local cu Stripe CLI
stripe listen --forward-to localhost:3001/api/webhooks/stripe
```

### Loguri
- Logurile sunt salvate în `logs/combined.log`
- Erorile în `logs/error.log`

### Testarea în dezvoltare
```bash
# Simulare webhook
curl -X POST http://localhost:3001/api/subscriptions/dev/simulate-webhook \
  -H "Content-Type: application/json" \
  -d '{"type": "customer.subscription.created"}'
```

## 📈 Monitorizare

- **Stripe Dashboard**: Monitorizarea plăților și abonamentelor
- **Logs**: Urmărirea evenimentelor și erorilor
- **Usage Stats**: Statistici de utilizare prin API

## 🚨 Troubleshooting

### Probleme comune

1. **Webhook-uri care nu funcționează**
   - Verificați `STRIPE_WEBHOOK_SECRET`
   - Asigurați-vă că URL-ul webhook-ului este accesibil

2. **Planuri care nu se sincronizează**
   - Verificați `STRIPE_SECRET_KEY`
   - Rulați `npm run stripe:plans`

3. **Limite care nu se aplică**
   - Verificați middleware-ul în rute
   - Verificați configurarea planurilor

### Support

Pentru probleme tehnice, verificați:
1. Logurile aplicației
2. Stripe Dashboard > Events
3. Documentația Stripe API

---

**Nota**: Această implementare folosește Stripe în modul test. Pentru producție, înlocuiți cheile de test cu cele live și configurați webhook-urile pentru domeniul de producție.