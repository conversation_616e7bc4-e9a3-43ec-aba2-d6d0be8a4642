# Ghid de Migrare: Trecerea la CUID și firstName/lastName

Acest document descrie pașii necesari pentru a migra baza de date și codul aplicației de la ID-uri numerice la CUID și de la câmpul `name` la `firstName` și `lastName`.

## Motivația Modificărilor

### 1. CUID în loc de ID-uri numerice
- **Securitate**: CUID-urile sunt mai greu de ghicit decât ID-urile secvențiale
- **Scalabilitate**: Permit distribuirea pe mai multe servere fără conflicte
- **Debugging**: Sunt mai ușor de identificat în log-uri (ex: `usr_abc123` vs `12345`)
- **API-uri**: Reduc riscul de enumerare a resurselor

### 2. firstName/lastName în loc de name
- **Flexibilitate**: Permite gestionarea separată a prenumelui și numelui
- **Internationalizare**: Funcționează mai bine cu diverse culturi
- **Sortare**: Permite sortarea după nume de familie
- **Personalizare**: Permite salutări mai personalizate

## Fișiere Create

1. **`backend/prisma/schema_updated.prisma`** - Noua schemă Prisma
2. **`frontend/src/types/updated_types.ts`** - Tipurile TypeScript actualizate
3. **`backend/prisma/migrations/migration_to_cuid_and_names.sql`** - Script de migrare SQL

## Planul de Implementare

### Faza 1: Pregătirea (1-2 zile)

1. **Backup complet al bazei de date**
   ```bash
   pg_dump -h localhost -U username -d database_name > backup_before_migration.sql
   ```

2. **Testarea pe un mediu de dezvoltare**
   - Creați o copie a bazei de date de producție
   - Testați scriptul de migrare
   - Verificați că toate funcționalitățile merg

3. **Instalarea dependințelor pentru CUID**
   ```bash
   npm install @paralleldrive/cuid2
   ```

### Faza 2: Actualizarea Backend-ului (2-3 zile)

1. **Înlocuirea schemei Prisma**
   ```bash
   cp backend/prisma/schema_updated.prisma backend/prisma/schema.prisma
   ```

2. **Generarea noului client Prisma**
   ```bash
   cd backend
   npx prisma generate
   ```

3. **Actualizarea serviciilor backend**
   - Modificați toate serviciile să folosească CUID-uri
   - Actualizați query-urile să folosească `firstName` și `lastName`
   - Testați toate endpoint-urile API

4. **Actualizarea validărilor**
   - Modificați validările pentru ID-uri (string în loc de number)
   - Actualizați validările pentru nume (firstName + lastName)

### Faza 3: Actualizarea Frontend-ului (2-3 zile)

1. **Înlocuirea tipurilor**
   ```bash
   cp frontend/src/types/updated_types.ts frontend/src/types/index.ts
   ```

2. **Actualizarea componentelor**
   - Modificați toate componentele să folosească `firstName` și `lastName`
   - Actualizați hook-urile să lucreze cu CUID-uri
   - Testați toate paginile și funcționalitățile

3. **Actualizarea serviciilor frontend**
   - Modificați `authService.ts` să trimită `firstName` și `lastName`
   - Actualizați toate apelurile API să folosească CUID-uri

### Faza 4: Migrarea Bazei de Date (1 zi)

1. **Oprirea aplicației**
   ```bash
   # Opriți serverul de producție
   pm2 stop all
   ```

2. **Executarea migrării**
   ```bash
   psql -h localhost -U username -d database_name -f backend/prisma/migrations/migration_to_cuid_and_names.sql
   ```

3. **Verificarea migrării**
   ```sql
   -- Verificați că datele au fost migrate corect
   SELECT id, firstName, lastName, email FROM users LIMIT 5;
   SELECT id, user_id FROM categories LIMIT 5;
   ```

4. **Aplicarea schemei Prisma**
   ```bash
   cd backend
   npx prisma db push
   ```

### Faza 5: Testarea și Lansarea (1 zi)

1. **Pornirea aplicației**
   ```bash
   pm2 start all
   ```

2. **Testarea funcționalităților**
   - Autentificare și înregistrare
   - Gestionarea profilului utilizator
   - CRUD pentru cheltuieli și categorii
   - Abonamente și plăți

3. **Monitorizarea**
   - Verificați log-urile pentru erori
   - Monitorizați performanța
   - Verificați că toate funcționalitățile merg

## Modificări Necesare în Cod

### Backend

1. **Servicii de autentificare**
   ```typescript
   // Înainte
   const user = await prisma.user.create({
     data: {
       email,
       password: hashedPassword,
       name: `${firstName} ${lastName}`
     }
   });
   
   // După
   const user = await prisma.user.create({
     data: {
       email,
       password: hashedPassword,
       firstName,
       lastName
     }
   });
   ```

2. **Query-uri**
   ```typescript
   // Înainte
   const user = await prisma.user.findUnique({
     where: { id: parseInt(userId) }
   });
   
   // După
   const user = await prisma.user.findUnique({
     where: { id: userId } // userId este deja string
   });
   ```

### Frontend

1. **Componente**
   ```typescript
   // Înainte
   <div>{user.name}</div>
   
   // După
   <div>{user.firstName} {user.lastName}</div>
   ```

2. **Formulare**
   ```typescript
   // Înainte
   const [name, setName] = useState('');
   
   // După
   const [firstName, setFirstName] = useState('');
   const [lastName, setLastName] = useState('');
   ```

## Riscuri și Măsuri de Precauție

### Riscuri
1. **Pierderea datelor** - Migrarea poate eșua
2. **Downtime prelungit** - Migrarea poate dura mai mult decât estimat
3. **Incompatibilități** - Codul nou poate avea bug-uri
4. **Probleme de performanță** - CUID-urile sunt mai mari decât ID-urile numerice

### Măsuri de Precauție
1. **Backup complet** înainte de migrare
2. **Testare extensivă** pe mediul de dezvoltare
3. **Plan de rollback** pregătit
4. **Monitorizare intensivă** după lansare
5. **Echipă de suport** disponibilă în timpul migrării

## Plan de Rollback

În cazul în care migrarea eșuează:

1. **Restaurarea bazei de date**
   ```bash
   psql -h localhost -U username -d database_name < backup_before_migration.sql
   ```

2. **Revenirea la codul vechi**
   ```bash
   git checkout HEAD~1  # sau commit-ul anterior
   npm install
   npm run build
   ```

3. **Repornirea serviciilor**
   ```bash
   pm2 restart all
   ```

## Cronograma Estimată

- **Săptămâna 1**: Pregătirea și testarea pe mediul de dezvoltare
- **Săptămâna 2**: Actualizarea backend-ului și frontend-ului
- **Weekend**: Migrarea bazei de date și lansarea
- **Săptămâna 3**: Monitorizare și rezolvarea eventualelor probleme

## Concluzie

Aceste modificări vor îmbunătăți semnificativ securitatea, scalabilitatea și flexibilitatea aplicației. Cu o planificare atentă și testare extensivă, migrarea poate fi realizată cu risc minim și beneficii pe termen lung.