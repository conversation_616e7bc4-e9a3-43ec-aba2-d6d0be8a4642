# 📚 API REFERENCE

## 🌐 BASE URL

```
Development: http://localhost:3000/api
Production: https://api.your-domain.com/api
```

## 🔐 AUTENTIFICARE

Toate endpoint-urile protejate necesită un Bearer token în header-ul Authorization:

```http
Authorization: Bearer <access_token>
```

### Obținerea Token-urilor

#### POST /auth/login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "<PERSON>",
      "role": "user",
      "subscriptionPlan": "free"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

#### POST /auth/refresh
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

---

## 👤 UTILIZATORI

### GET /users/profile
Obține profilul utilizatorului curent.

```http
GET /api/users/profile
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "avatar": "https://example.com/avatar.jpg",
    "subscriptionPlan": "premium",
    "subscriptionStatus": "active",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

### PUT /users/profile
Actualizează profilul utilizatorului.

```http
PUT /api/users/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "John Smith",
  "avatar": "https://example.com/new-avatar.jpg"
}
```

---

## 💰 CHELTUIELI

### GET /expenses
Obține lista cheltuielilor utilizatorului.

```http
GET /api/expenses?page=1&limit=20&category=food&dateFrom=2024-01-01&dateTo=2024-01-31
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` (optional): Numărul paginii (default: 1)
- `limit` (optional): Numărul de rezultate per pagină (default: 20, max: 100)
- `category` (optional): ID-ul categoriei pentru filtrare
- `dateFrom` (optional): Data de început (YYYY-MM-DD)
- `dateTo` (optional): Data de sfârșit (YYYY-MM-DD)
- `search` (optional): Căutare în descriere

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "exp_123",
      "amount": 25.50,
      "description": "Lunch at restaurant",
      "expenseDate": "2024-01-15",
      "category": {
        "id": "cat_123",
        "name": "Food",
        "icon": "🍽️",
        "color": "#FF6B6B"
      },
      "tags": ["restaurant", "business"],
      "createdAt": "2024-01-15T14:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### POST /expenses
Creează o cheltuială nouă.

```http
POST /api/expenses
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 25.50,
  "description": "Lunch at restaurant",
  "categoryId": "cat_123",
  "expenseDate": "2024-01-15",
  "tags": ["restaurant", "business"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "exp_123",
    "amount": 25.50,
    "description": "Lunch at restaurant",
    "expenseDate": "2024-01-15",
    "category": {
      "id": "cat_123",
      "name": "Food",
      "icon": "🍽️",
      "color": "#FF6B6B"
    },
    "tags": ["restaurant", "business"],
    "createdAt": "2024-01-15T14:30:00Z"
  }
}
```

### PUT /expenses/:id
Actualizează o cheltuială existentă.

```http
PUT /api/expenses/exp_123
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 30.00,
  "description": "Updated lunch expense"
}
```

### DELETE /expenses/:id
Șterge o cheltuială.

```http
DELETE /api/expenses/exp_123
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "Expense deleted successfully"
}
```

---

## 📂 CATEGORII

### GET /categories
Obține lista categoriilor (default + personalizate).

```http
GET /api/categories
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "cat_123",
      "name": "Food",
      "icon": "🍽️",
      "color": "#FF6B6B",
      "isDefault": true,
      "expenseCount": 25
    },
    {
      "id": "cat_456",
      "name": "Custom Category",
      "icon": "💼",
      "color": "#4ECDC4",
      "isDefault": false,
      "expenseCount": 5
    }
  ]
}
```

### POST /categories
Creează o categorie personalizată.

```http
POST /api/categories
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Custom Category",
  "icon": "💼",
  "color": "#4ECDC4"
}
```

---

## 📊 RAPOARTE

### GET /reports/monthly
Obține raportul lunar.

```http
GET /api/reports/monthly?year=2024&month=1
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "month": "2024-01",
    "totalAmount": 1250.75,
    "expenseCount": 45,
    "averagePerDay": 40.35,
    "categories": [
      {
        "categoryId": "cat_123",
        "categoryName": "Food",
        "amount": 450.25,
        "count": 18,
        "percentage": 36.02
      }
    ],
    "dailyBreakdown": [
      {
        "date": "2024-01-01",
        "amount": 25.50,
        "count": 2
      }
    ]
  }
}
```

### GET /reports/category
Raport pe categorii.

```http
GET /api/reports/category?period=last_6_months
Authorization: Bearer <token>
```

### GET /reports/trends (Premium)
Analiză tendințe avansată.

```http
GET /api/reports/trends?period=last_year
Authorization: Bearer <token>
```

---

## 📤 EXPORT

### GET /export/csv
Export CSV.

```http
GET /api/export/csv?dateFrom=2024-01-01&dateTo=2024-01-31
Authorization: Bearer <token>
```

**Response:**
```
Content-Type: text/csv
Content-Disposition: attachment; filename="expenses_2024-01.csv"

Date,Amount,Description,Category
2024-01-15,25.50,"Lunch at restaurant",Food
2024-01-16,12.00,"Coffee",Food
```

### GET /export/pdf (Premium)
Export PDF.

```http
GET /api/export/pdf?dateFrom=2024-01-01&dateTo=2024-01-31
Authorization: Bearer <token>
```

### GET /export/excel (Premium)
Export Excel.

```http
GET /api/export/excel?dateFrom=2024-01-01&dateTo=2024-01-31
Authorization: Bearer <token>
```

---

## 💳 ABONAMENTE

### GET /subscriptions/plans
Obține planurile disponibile.

```http
GET /api/subscriptions/plans
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "free",
      "name": "Free",
      "price": 0,
      "currency": "USD",
      "interval": "month",
      "features": [
        "50 expenses per month",
        "5 custom categories",
        "Basic reports"
      ],
      "limits": {
        "expenses": 50,
        "categories": 5,
        "exports": 3
      }
    },
    {
      "id": "premium",
      "name": "Premium",
      "price": 19.99,
      "currency": "USD",
      "interval": "month",
      "priceId": "price_premium_monthly",
      "features": [
        "Unlimited expenses",
        "Unlimited categories",
        "Advanced reports",
        "All export formats"
      ],
      "popular": true
    }
  ]
}
```

### POST /subscriptions/checkout
Creează sesiune Stripe Checkout.

```http
POST /api/subscriptions/checkout
Authorization: Bearer <token>
Content-Type: application/json

{
  "priceId": "price_premium_monthly"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "cs_test_123...",
    "url": "https://checkout.stripe.com/pay/cs_test_123..."
  }
}
```

### GET /subscriptions/current
Obține abonamentul curent.

```http
GET /api/subscriptions/current
Authorization: Bearer <token>
```

### POST /subscriptions/portal
Creează sesiune Customer Portal.

```http
POST /api/subscriptions/portal
Authorization: Bearer <token>
```

---

## 📈 UTILIZARE ȘI LIMITE

### GET /usage/current
Obține utilizarea curentă.

```http
GET /api/usage/current
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "currentPeriod": {
      "expenses": 25,
      "categories": 3,
      "exports": 1
    },
    "limits": {
      "expenses": 50,
      "categories": 5,
      "exports": 3
    },
    "usage": {
      "expenses": 50.0,
      "categories": 60.0,
      "exports": 33.3
    },
    "planType": "free"
  }
}
```

---

## 🔧 ADMIN (Doar Administratori)

### GET /admin/users
Lista utilizatori.

```http
GET /api/admin/users?page=1&limit=50
Authorization: Bearer <admin_token>
```

### GET /admin/stats
Statistici generale.

```http
GET /api/admin/stats
Authorization: Bearer <admin_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalUsers": 1250,
    "activeUsers": 890,
    "totalRevenue": 15750.00,
    "subscriptions": {
      "free": 950,
      "basic": 200,
      "premium": 100
    },
    "monthlyGrowth": 12.5
  }
}
```

---

## ❌ CODURI DE EROARE

### Coduri HTTP Standard
- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Unprocessable Entity
- `429` - Too Many Requests
- `500` - Internal Server Error

### Format Răspuns Eroare
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "amount",
      "message": "Amount must be positive"
    }
  ],
  "code": "VALIDATION_ERROR"
}
```

### Coduri de Eroare Personalizate
- `VALIDATION_ERROR` - Eroare de validare
- `AUTHENTICATION_FAILED` - Autentificare eșuată
- `INSUFFICIENT_PERMISSIONS` - Permisiuni insuficiente
- `RESOURCE_NOT_FOUND` - Resursa nu a fost găsită
- `SUBSCRIPTION_REQUIRED` - Abonament necesar
- `USAGE_LIMIT_EXCEEDED` - Limita de utilizare depășită
- `RATE_LIMIT_EXCEEDED` - Prea multe cereri

---

## 🔄 RATE LIMITING

### Limite Generale
- **Utilizatori autentificați**: 1000 cereri per 15 minute
- **Utilizatori neautentificați**: 100 cereri per 15 minute
- **Endpoint-uri de autentificare**: 5 încercări per 15 minute per IP

### Headers Rate Limiting
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

---

*Ultima actualizare: Ianuarie 2025*
