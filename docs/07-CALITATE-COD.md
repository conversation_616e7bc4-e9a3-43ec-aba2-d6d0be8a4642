# 🏆 CALITATEA CODULUI ȘI BEST PRACTICES

## 📋 STANDARDELE DE CALITATE

### Principii Fundamentale
1. **SOLID Principles** - Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion
2. **DRY (Don't Repeat Yourself)** - Evitarea duplicării codului
3. **KISS (Keep It Simple Stupid)** - Simplitatea și claritatea
4. **YAGNI (You Aren't Gonna Need It)** - Nu implementa funcționalități care nu sunt necesare acum
5. **Clean Code** - Cod lizibil, mentenabil și testabil

### Metrici de Calitate
- **Test Coverage**: Minimum 85% pentru cod critic
- **Cyclomatic Complexity**: Maximum 10 per funcție
- **Code Duplication**: Maximum 3%
- **Technical Debt**: Maximum 30 minute per 1000 linii de cod
- **ESLint Errors**: 0 toleranță pentru erori

---

## 🔧 CONFIGURĂRI DEZVOLTARE

### ESLint Configuration

#### Backend ESLint
```javascript
// backend/.eslintrc.js
module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    project: './tsconfig.json',
  },
  plugins: ['@typescript-eslint', 'import', 'security'],
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    '@typescript-eslint/recommended-requiring-type-checking',
    'plugin:import/recommended',
    'plugin:import/typescript',
    'plugin:security/recommended',
  ],
  rules: {
    // TypeScript specific
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/prefer-nullish-coalescing': 'error',
    '@typescript-eslint/prefer-optional-chain': 'error',
    
    // Import rules
    'import/order': [
      'error',
      {
        groups: [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index',
        ],
        'newlines-between': 'always',
      },
    ],
    'import/no-unresolved': 'error',
    'import/no-cycle': 'error',
    
    // Security rules
    'security/detect-object-injection': 'error',
    'security/detect-sql-injection': 'error',
    
    // General rules
    'no-console': 'warn',
    'no-debugger': 'error',
    'prefer-const': 'error',
    'no-var': 'error',
    'eqeqeq': 'error',
    'curly': 'error',
  },
  env: {
    node: true,
    es2022: true,
    jest: true,
  },
  settings: {
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json',
      },
    },
  },
};
```

#### Frontend ESLint
```javascript
// frontend/.eslintrc.js
module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
    project: './tsconfig.json',
  },
  plugins: [
    '@typescript-eslint',
    'react',
    'react-hooks',
    'jsx-a11y',
    'import',
  ],
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:jsx-a11y/recommended',
    'plugin:import/recommended',
    'plugin:import/typescript',
  ],
  rules: {
    // React specific
    'react/react-in-jsx-scope': 'off', // React 17+
    'react/prop-types': 'off', // Using TypeScript
    'react/jsx-uses-react': 'off',
    'react/jsx-uses-vars': 'error',
    'react/jsx-key': 'error',
    'react/no-array-index-key': 'warn',
    'react/jsx-no-bind': 'warn',
    
    // React Hooks
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    
    // Accessibility
    'jsx-a11y/alt-text': 'error',
    'jsx-a11y/anchor-is-valid': 'error',
    'jsx-a11y/click-events-have-key-events': 'warn',
    
    // TypeScript
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-explicit-any': 'error',
    
    // Import rules
    'import/order': [
      'error',
      {
        groups: [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index',
        ],
        'newlines-between': 'always',
        pathGroups: [
          {
            pattern: 'react',
            group: 'external',
            position: 'before',
          },
        ],
        pathGroupsExcludedImportTypes: ['react'],
      },
    ],
  },
  env: {
    browser: true,
    es2022: true,
    node: true,
  },
  settings: {
    react: {
      version: 'detect',
    },
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json',
      },
    },
  },
};
```

### Prettier Configuration
```javascript
// .prettierrc.js
module.exports = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  printWidth: 80,
  tabWidth: 2,
  useTabs: false,
  bracketSpacing: true,
  bracketSameLine: false,
  arrowParens: 'avoid',
  endOfLine: 'lf',
  quoteProps: 'as-needed',
  jsxSingleQuote: true,
  
  // File-specific overrides
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 120,
      },
    },
    {
      files: '*.md',
      options: {
        printWidth: 100,
        proseWrap: 'always',
      },
    },
  ],
};
```

### TypeScript Configuration

#### Strict TypeScript Config
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": false,
    "importHelpers": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/hooks/*": ["src/hooks/*"],
      "@/services/*": ["src/services/*"],
      "@/utils/*": ["src/utils/*"],
      "@/types/*": ["src/types/*"]
    }
  },
  "include": [
    "src/**/*",
    "tests/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build"
  ]
}
```

---

## 🏗️ ARHITECTURA CODULUI

### Structura Directoarelor

#### Backend Structure
```
backend/src/
├── config/          # Configurări (database, redis, etc.)
├── controllers/     # Request handlers
├── middleware/      # Express middleware
├── services/        # Business logic
├── models/          # Data models și validări
├── routes/          # Route definitions
├── utils/           # Utility functions
├── types/           # TypeScript type definitions
├── constants/       # Application constants
└── tests/           # Test files
```

#### Frontend Structure
```
frontend/src/
├── components/      # React components
│   ├── ui/         # Reusable UI components
│   ├── layout/     # Layout components
│   ├── forms/      # Form components
│   └── charts/     # Chart components
├── pages/          # Page components
├── hooks/          # Custom React hooks
├── services/       # API services
├── store/          # State management
├── utils/          # Utility functions
├── types/          # TypeScript types
├── constants/      # Application constants
└── tests/          # Test files
```

### Naming Conventions

#### File Naming
```typescript
// Components: PascalCase
ExpenseForm.tsx
UserProfile.tsx
DashboardLayout.tsx

// Hooks: camelCase starting with 'use'
useExpenses.ts
useAuth.ts
useLocalStorage.ts

// Services: camelCase ending with 'Service'
expenseService.ts
authService.ts
stripeService.ts

// Utils: camelCase
formatCurrency.ts
validateEmail.ts
dateHelpers.ts

// Types: camelCase ending with 'Types'
expenseTypes.ts
userTypes.ts
apiTypes.ts
```

#### Variable Naming
```typescript
// Constants: SCREAMING_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// Variables: camelCase
const userName = 'john_doe';
const isAuthenticated = true;
const expenseList = [];

// Functions: camelCase (verb + noun)
function calculateTotal() {}
function validateInput() {}
function formatCurrency() {}

// Classes: PascalCase
class ExpenseService {}
class UserController {}
class DatabaseConnection {}

// Interfaces: PascalCase starting with 'I' (optional)
interface User {}
interface IExpenseRepository {} // if needed for distinction
```

### Code Organization Patterns

#### Service Layer Pattern
```typescript
// src/services/expenseService.ts
import { PrismaClient } from '@prisma/client';
import { CreateExpenseDto, UpdateExpenseDto, ExpenseFilters } from '../types/expenseTypes';

export class ExpenseService {
  constructor(private prisma: PrismaClient) {}

  async createExpense(userId: string, data: CreateExpenseDto): Promise<Expense> {
    // Validation
    this.validateExpenseData(data);
    
    // Business logic
    const expense = await this.prisma.expense.create({
      data: {
        ...data,
        userId,
      },
      include: {
        category: true,
      },
    });

    // Post-processing
    await this.updateUsageStats(userId);
    
    return expense;
  }

  async getExpenses(userId: string, filters: ExpenseFilters): Promise<ExpenseListResult> {
    const where = this.buildWhereClause(userId, filters);
    
    const [expenses, total] = await Promise.all([
      this.prisma.expense.findMany({
        where,
        include: { category: true },
        orderBy: { expenseDate: 'desc' },
        take: filters.limit,
        skip: filters.offset,
      }),
      this.prisma.expense.count({ where }),
    ]);

    return { expenses, total };
  }

  private validateExpenseData(data: CreateExpenseDto): void {
    if (data.amount <= 0) {
      throw new ValidationError('Amount must be positive');
    }
    
    if (!data.description?.trim()) {
      throw new ValidationError('Description is required');
    }
  }

  private buildWhereClause(userId: string, filters: ExpenseFilters) {
    return {
      userId,
      ...(filters.categoryId && { categoryId: filters.categoryId }),
      ...(filters.dateFrom && { expenseDate: { gte: filters.dateFrom } }),
      ...(filters.dateTo && { expenseDate: { lte: filters.dateTo } }),
    };
  }
}
```

#### Repository Pattern (Optional)
```typescript
// src/repositories/expenseRepository.ts
import { PrismaClient } from '@prisma/client';

export class ExpenseRepository {
  constructor(private prisma: PrismaClient) {}

  async findByUserId(userId: string, options: FindOptions = {}): Promise<Expense[]> {
    return this.prisma.expense.findMany({
      where: { userId },
      ...options,
    });
  }

  async create(data: CreateExpenseData): Promise<Expense> {
    return this.prisma.expense.create({ data });
  }

  async update(id: string, data: UpdateExpenseData): Promise<Expense> {
    return this.prisma.expense.update({
      where: { id },
      data,
    });
  }

  async delete(id: string): Promise<void> {
    await this.prisma.expense.delete({ where: { id } });
  }
}
```

### Error Handling Patterns

#### Custom Error Classes
```typescript
// src/utils/errors.ts
export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public isOperational: boolean = true
  ) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400);
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string) {
    super(`${resource} not found`, 404);
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401);
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden') {
    super(message, 403);
  }
}
```

#### Error Handler Middleware
```typescript
// src/middleware/errorHandler.ts
import { Request, Response, NextFunction } from 'express';
import { AppError } from '../utils/errors';
import { logger } from '../utils/logger';

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let error = err;

  // Convert known errors to AppError
  if (err.name === 'ValidationError') {
    error = new AppError('Validation Error', 400);
  } else if (err.name === 'CastError') {
    error = new AppError('Invalid ID format', 400);
  } else if (err.name === 'JsonWebTokenError') {
    error = new AppError('Invalid token', 401);
  }

  // Log error
  logger.error({
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name,
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    },
  });

  // Send error response
  if (error instanceof AppError) {
    res.status(error.statusCode).json({
      success: false,
      message: error.message,
      ...(process.env['NODE_ENV'] === 'development' && { stack: error.stack }),
    });
  } else {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      ...(process.env['NODE_ENV'] === 'development' && { 
        originalError: error.message,
        stack: error.stack 
      }),
    });
  }
};
```

---

## 🧪 TESTARE ȘI CALITATE

### Test Structure
```typescript
// tests/services/expenseService.test.ts
import { ExpenseService } from '../../src/services/expenseService';
import { prismaMock } from '../mocks/prisma';
import { ValidationError } from '../../src/utils/errors';

describe('ExpenseService', () => {
  let expenseService: ExpenseService;

  beforeEach(() => {
    expenseService = new ExpenseService(prismaMock);
  });

  describe('createExpense', () => {
    it('should create expense with valid data', async () => {
      // Arrange
      const userId = 'user-123';
      const expenseData = {
        amount: 25.50,
        description: 'Test expense',
        categoryId: 'cat-123',
        expenseDate: new Date('2024-01-15'),
      };

      const expectedExpense = {
        id: 'exp-123',
        ...expenseData,
        userId,
        category: { id: 'cat-123', name: 'Food' },
      };

      prismaMock.expense.create.mockResolvedValue(expectedExpense);

      // Act
      const result = await expenseService.createExpense(userId, expenseData);

      // Assert
      expect(result).toEqual(expectedExpense);
      expect(prismaMock.expense.create).toHaveBeenCalledWith({
        data: { ...expenseData, userId },
        include: { category: true },
      });
    });

    it('should throw ValidationError for negative amount', async () => {
      // Arrange
      const userId = 'user-123';
      const invalidData = {
        amount: -10,
        description: 'Invalid expense',
        categoryId: 'cat-123',
        expenseDate: new Date(),
      };

      // Act & Assert
      await expect(
        expenseService.createExpense(userId, invalidData)
      ).rejects.toThrow(ValidationError);
    });
  });
});
```

### Code Quality Scripts
```json
// package.json scripts
{
  "scripts": {
    "lint": "eslint . --ext .ts,.tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext .ts,.tsx --fix",
    "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"",
    "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false",
    "quality": "npm run lint && npm run format:check && npm run type-check && npm run test:coverage",
    "pre-commit": "lint-staged"
  }
}
```

---

## 📊 METRICI ȘI MONITORING

### Code Quality Metrics
```typescript
// Quality thresholds
const QUALITY_THRESHOLDS = {
  testCoverage: {
    statements: 85,
    branches: 80,
    functions: 85,
    lines: 85,
  },
  complexity: {
    maxComplexity: 10,
    maxDepth: 4,
    maxParams: 5,
  },
  maintainability: {
    minMaintainabilityIndex: 70,
    maxTechnicalDebt: 30, // minutes per 1000 lines
  },
  duplication: {
    maxDuplication: 3, // percentage
  },
};
```

### Pre-commit Hooks
```javascript
// .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npm run lint
npm run format:check
npm run type-check
npm run test:ci
```

---

*Ultima actualizare: Ianuarie 2025*
