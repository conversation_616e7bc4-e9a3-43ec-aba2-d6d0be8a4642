# 📋 **CODING STANDARDS - FinanceNinja**

## **🎯 OBIECTIV**

Acest document definește standardele de cod pentru proiectul FinanceNinja pentru a asigura:
- **Consistență** în întregul codebase
- **Calitate** și **mentenabilitate** a codului
- **Colaborare** eficientă în echipă
- **Performanță** și **securitate** optimă

---

## **🛠️ CONFIGURARE DEZVOLTARE**

### **ESLint & Prettier**
- ✅ **ESLint** configurat pentru TypeScript, React și Node.js
- ✅ **Prettier** pentru formatare automată
- ✅ **Pre-commit hooks** cu Husky și lint-staged
- ✅ **VS Code** extensii recomandate în `.vscode/extensions.json`

### **Comenzi Esențiale**
```bash
# Frontend
npm run lint          # Verifică erorile de linting
npm run lint:fix       # Corectează automat erorile
npm run format         # Formatează codul cu Prettier
npm run type-check     # Verifică erorile TypeScript

# Backend
npm run lint          # Verifică erorile de linting
npm run lint:fix       # Corectează automat erorile
npm run format         # Formatează codul cu Prettier
npm run build         # Compilează TypeScript
```

---

## **📝 CONVENȚII DE NOMENCLATURĂ**

### **🎨 Frontend (React/TypeScript)**

#### **Fișiere și Directoare**
```
✅ CORECT:
- components/ui/Button.tsx
- hooks/useAuth.ts
- utils/helpers.ts
- types/api.ts

❌ INCORECT:
- components/ui/button.tsx
- hooks/use-auth.ts
- utils/Helpers.ts
```

#### **Componente React**
```typescript
✅ CORECT:
// PascalCase pentru componente
const UserProfile: React.FC<UserProfileProps> = ({ user }) => {
  return <div>{user.name}</div>;
};

// camelCase pentru props și variabile
interface UserProfileProps {
  user: User;
  isLoading?: boolean;
  onUpdate?: (user: User) => void;
}
```

#### **Hooks Personalizate**
```typescript
✅ CORECT:
const useAuthStore = () => {
  const [user, setUser] = useState<User | null>(null);
  return { user, setUser };
};

const useLocalStorage = <T>(key: string, defaultValue: T) => {
  // implementare
};
```

### **🔧 Backend (Node.js/TypeScript)**

#### **Fișiere și Directoare**
```
✅ CORECT:
- controllers/userController.ts
- services/authService.ts
- middleware/authMiddleware.ts
- utils/errorHelpers.ts

❌ INCORECT:
- controllers/UserController.ts
- services/auth-service.ts
- middleware/auth_middleware.ts
```

#### **Funcții și Variabile**
```typescript
✅ CORECT:
// camelCase pentru funcții și variabile
const getUserById = async (id: string): Promise<User | null> => {
  return await prisma.user.findUnique({ where: { id } });
};

const validateUserInput = (data: CreateUserDto): ValidationResult => {
  // implementare
};
```

### **🗄️ Baza de Date**

#### **Tabele și Coloane**
```sql
✅ CORECT (camelCase în Prisma):
model User {
  id        String   @id @default(cuid())
  firstName String
  lastName  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

❌ INCORECT:
model user {
  ID         String
  first_name String
  last_name  String
}
```

---

## **🏗️ STRUCTURA PROIECTULUI**

### **Frontend Structure**
```
src/
├── components/          # Componente React
│   ├── ui/             # Componente UI reutilizabile
│   ├── layout/         # Layout-uri (Header, Sidebar, etc.)
│   └── features/       # Componente specifice funcționalităților
├── hooks/              # Custom hooks
├── services/           # API calls și servicii
├── store/              # State management (Zustand)
├── types/              # TypeScript type definitions
├── utils/              # Funcții utilitare
└── styles/             # Stiluri globale și Tailwind
```

### **Backend Structure**
```
src/
├── controllers/        # Route handlers
├── services/           # Business logic
├── middleware/         # Express middleware
├── models/             # Prisma models (generated)
├── types/              # TypeScript interfaces
├── utils/              # Funcții utilitare
├── config/             # Configurări (DB, auth, etc.)
└── routes/             # Route definitions
```

---

## **🎨 STILURI ȘI UI**

### **Tailwind CSS**
```typescript
✅ CORECT:
// Folosește clase Tailwind consistente
<button className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
  Click me
</button>

// Folosește cn() helper pentru clase condiționale
<div className={cn(
  'base-classes',
  isActive && 'active-classes',
  className
)}>
```

### **Componente UI**
```typescript
✅ CORECT:
// Definește props-uri clare cu TypeScript
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

// Folosește forwardRef pentru componente UI
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = 'primary', size = 'md', ...props }, ref) => {
    return <button ref={ref} {...props} />;
  }
);
```

---

## **🔒 SECURITATE ȘI BEST PRACTICES**

### **Validare Input**
```typescript
✅ CORECT:
// Validează TOATE input-urile utilizatorului
const createUser = async (req: Request, res: Response) => {
  const { error, value } = userSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ error: error.details[0].message });
  }
  
  // Procesează datele validate
  const user = await userService.create(value);
  res.json(user);
};
```

### **Gestionarea Erorilor**
```typescript
✅ CORECT:
// Folosește try-catch și logging consistent
const getUserProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = await userService.getById(req.user!.id);
    res.json({ success: true, data: user });
  } catch (error) {
    logger.error('Get user profile error:', error as Error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
};
```

### **TypeScript Safety**
```typescript
✅ CORECT:
// Evită 'any', folosește tipuri specifice
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Folosește type guards
const isUser = (obj: unknown): obj is User => {
  return typeof obj === 'object' && obj !== null && 'id' in obj;
};
```

---

## **📊 PERFORMANȚĂ**

### **React Optimizations**
```typescript
✅ CORECT:
// Folosește React.memo pentru componente pure
const ExpensiveComponent = React.memo<Props>(({ data }) => {
  return <div>{data.name}</div>;
});

// Folosește useMemo și useCallback pentru optimizări
const MemoizedComponent: React.FC<Props> = ({ items, onSelect }) => {
  const sortedItems = useMemo(() => 
    items.sort((a, b) => a.name.localeCompare(b.name)), 
    [items]
  );
  
  const handleSelect = useCallback((item: Item) => {
    onSelect(item);
  }, [onSelect]);
  
  return (
    <div>
      {sortedItems.map(item => (
        <ItemComponent 
          key={item.id} 
          item={item} 
          onSelect={handleSelect} 
        />
      ))}
    </div>
  );
};
```

### **Bundle Optimization**
```typescript
✅ CORECT:
// Lazy loading pentru rute
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Reports = lazy(() => import('./pages/Reports'));

// Code splitting pentru biblioteci mari
const ChartComponent = lazy(() => import('./components/Chart'));
```

---

## **🧪 TESTARE**

### **Structura Testelor**
```
tests/
├── unit/              # Teste unitare
├── integration/       # Teste de integrare
├── e2e/              # Teste end-to-end
├── fixtures/         # Date de test
└── utils/            # Utilitare pentru teste
```

### **Convenții Testare**
```typescript
✅ CORECT:
describe('UserService', () => {
  describe('createUser', () => {
    it('should create user with valid data', async () => {
      // Arrange
      const userData = { email: '<EMAIL>', name: 'Test User' };
      
      // Act
      const result = await userService.createUser(userData);
      
      // Assert
      expect(result).toBeDefined();
      expect(result.email).toBe(userData.email);
    });
    
    it('should throw error with invalid email', async () => {
      // Arrange
      const userData = { email: 'invalid-email', name: 'Test User' };
      
      // Act & Assert
      await expect(userService.createUser(userData)).rejects.toThrow();
    });
  });
});
```

---

## **📚 DOCUMENTAȚIE**

### **Comentarii în Cod**
```typescript
✅ CORECT:
/**
 * Calculează totalul cheltuielilor pentru o perioadă specificată
 * @param expenses - Lista cheltuielilor
 * @param startDate - Data de început
 * @param endDate - Data de sfârșit
 * @returns Totalul cheltuielilor în perioada specificată
 */
const calculateTotal = (
  expenses: Expense[], 
  startDate: Date, 
  endDate: Date
): number => {
  return expenses
    .filter(expense => 
      expense.date >= startDate && expense.date <= endDate
    )
    .reduce((total, expense) => total + expense.amount, 0);
};

// Comentariu pentru logică complexă
// Folosim debounce pentru a evita apelurile API excesive
const debouncedSearch = useMemo(
  () => debounce(searchFunction, 300),
  [searchFunction]
);
```

### **README Files**
- ✅ Fiecare modul important să aibă README.md
- ✅ Instrucțiuni clare de instalare și utilizare
- ✅ Exemple de cod pentru API-uri
- ✅ Troubleshooting pentru probleme comune

---

## **🔄 WORKFLOW DEZVOLTARE**

### **Git Workflow**
```bash
# 1. Creează branch pentru feature
git checkout -b feature/user-authentication

# 2. Dezvoltă și testează
npm run lint
npm run test
npm run build

# 3. Commit cu mesaje descriptive
git commit -m "feat: add user authentication with JWT"

# 4. Push și creează PR
git push origin feature/user-authentication
```

### **Commit Messages**
```
✅ CORECT:
feat: add user authentication system
fix: resolve memory leak in expense calculation
docs: update API documentation for user endpoints
refactor: simplify expense filtering logic
test: add unit tests for user service

❌ INCORECT:
- updated stuff
- fix bug
- changes
```

---

## **⚡ COMENZI RAPIDE**

```bash
# Verificare completă calitate cod
npm run lint && npm run type-check && npm run test

# Formatare și fix automat
npm run format && npm run lint:fix

# Build și verificare producție
npm run build && npm run preview

# Cleanup și reinstalare
rm -rf node_modules package-lock.json && npm install
```

---

## **📞 SUPORT**

Pentru întrebări despre coding standards:
1. 📖 Consultă această documentație
2. 🔍 Verifică exemplele din codebase
3. 💬 Discută cu echipa în code review
4. 📝 Propune îmbunătățiri prin PR

**Menține codul curat, consistent și documentat!** 🚀
