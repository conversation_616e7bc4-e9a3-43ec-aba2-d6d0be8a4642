# 🚀 SCRIPT AUTOMATIZARE - IMPLEMENTARE FUNCȚIONALĂ
# Acest script automatizează pașii din planul de implementare

Write-Host "🚀 ÎNCEPE IMPLEMENTAREA FUNCȚIONALĂ" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green

# Funcție pentru logging
function Write-Step {
    param([string]$Message, [string]$Color = "Cyan")
    Write-Host "\n📋 $Message" -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

# Verifică dacă suntem în directorul corect
if (-not (Test-Path "package.json" -PathType Leaf)) {
    Write-Error "Nu suntem în directorul rădăcină al proiectului!"
    Write-Host "Navighează în directorul f:\proiecte\fn și rulează din nou scriptul."
    exit 1
}

Write-Step "FAZA 1: VERIFICARE CONFIGURAȚII CRITICE" "Yellow"

# 1.1 Verifică configurațiile environment
Write-Step "Verifică fișierele .env..."

if (Test-Path "backend\.env") {
    Write-Success "backend/.env există"
} else {
    Write-Warning "backend/.env lipsește - va fi creat din .env.example"
    if (Test-Path "backend\.env.example") {
        Copy-Item "backend\.env.example" "backend\.env"
        Write-Success "backend/.env creat din .env.example"
    } else {
        Write-Error "backend/.env.example nu există!"
    }
}

if (Test-Path "frontend\.env") {
    Write-Success "frontend/.env există"
} else {
    Write-Warning "frontend/.env lipsește - va fi creat"
    $frontendEnv = @'
# API Configuration
REACT_APP_API_URL=http://localhost:3000/api
REACT_APP_API_BASE_URL=http://localhost:3000

# Stripe Configuration
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here

# Environment
NODE_ENV=development

# App Configuration
REACT_APP_APP_NAME=FinanceApp
REACT_APP_VERSION=1.0.0

# Debug
REACT_APP_DEBUG=true

# Features
REACT_APP_ENABLE_PWA=true
REACT_APP_ENABLE_ANALYTICS=false

# Security
REACT_APP_ENABLE_HTTPS=false
REACT_APP_SECURE_COOKIES=false

# Development
REACT_APP_MOCK_API=false
REACT_APP_LOG_LEVEL=debug
'@
    $frontendEnv | Out-File -FilePath "frontend\.env" -Encoding UTF8
    Write-Success "frontend/.env creat cu configurații de bază"
}

# 1.2 Verifică și instalează dependențele
Write-Step "Verifică dependențele..."

Write-Host "\n🔧 Verifică dependențele backend..."
Set-Location "backend"
if (-not (Test-Path "node_modules")) {
    Write-Warning "node_modules lipsește în backend - instalează dependențele..."
    npm install
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Dependențele backend instalate cu succes"
    } else {
        Write-Error "Eroare la instalarea dependențelor backend"
        Set-Location ".."
        exit 1
    }
} else {
    Write-Success "Dependențele backend sunt instalate"
}

Set-Location ".."

Write-Host "\n🔧 Verifică dependențele frontend..."
Set-Location "frontend"
if (-not (Test-Path "node_modules")) {
    Write-Warning "node_modules lipsește în frontend - instalează dependențele..."
    npm install
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Dependențele frontend instalate cu succes"
    } else {
        Write-Error "Eroare la instalarea dependențelor frontend"
        Set-Location ".."
        exit 1
    }
} else {
    Write-Success "Dependențele frontend sunt instalate"
}

Set-Location ".."

# 1.3 Testează build-urile
Write-Step "TESTARE BUILD-URI" "Yellow"

Write-Host "\n🏗️ Testează build backend..."
Set-Location "backend"
npm run build
if ($LASTEXITCODE -eq 0) {
    Write-Success "Build backend reușit"
} else {
    Write-Error "Build backend eșuat - verifică erorile TypeScript"
    Write-Host "\n🔍 Rulează 'npm run type-check' pentru detalii"
}

Set-Location ".."

Write-Host "\n🏗️ Testează build frontend..."
Set-Location "frontend"
npm run build
if ($LASTEXITCODE -eq 0) {
    Write-Success "Build frontend reușit"
    
    # Verifică mărimea bundle-ului
    if (Test-Path "dist") {
        $bundleSize = (Get-ChildItem "dist" -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
        Write-Host "📦 Mărime bundle: $([math]::Round($bundleSize, 2)) MB"
        
        if ($bundleSize -gt 2) {
            Write-Warning "Bundle-ul este prea mare (>2MB) - necesită optimizare"
        } else {
            Write-Success "Mărimea bundle-ului este acceptabilă"
        }
    }
} else {
    Write-Error "Build frontend eșuat - verifică erorile"
}

Set-Location ".."

# 1.4 Verifică baza de date
Write-Step "VERIFICARE BAZĂ DE DATE" "Yellow"

Set-Location "backend"
Write-Host "\n🗄️ Verifică conexiunea la baza de date..."
npm run db:status 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Success "Conexiunea la baza de date funcționează"
} else {
    Write-Warning "Probleme cu baza de date - încearcă să reseteze..."
    npm run db:reset
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Baza de date resetată cu succes"
    } else {
        Write-Error "Eroare la resetarea bazei de date"
    }
}

Set-Location ".."

# 1.5 Testează serverele
Write-Step "TESTARE SERVERE" "Yellow"

Write-Host "\n🚀 Pornește serverul backend în background..."
Set-Location "backend"
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    npm run dev
}

Start-Sleep -Seconds 10

# Testează dacă backend-ul răspunde
Write-Host "\n🔍 Testează API backend..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/api/health" -TimeoutSec 5 -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Success "Backend API răspunde corect"
    }
} catch {
    Write-Warning "Backend API nu răspunde - verifică logs"
    Write-Host "Logs backend job: "
    Receive-Job $backendJob
}

Set-Location ".."

# Oprește job-ul backend
Stop-Job $backendJob -ErrorAction SilentlyContinue
Remove-Job $backendJob -ErrorAction SilentlyContinue

# 1.6 Rulează testele
Write-Step "RULARE TESTE" "Yellow"

Write-Host "\n🧪 Rulează testele backend..."
Set-Location "backend"
npm run test 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Success "Testele backend trec"
} else {
    Write-Warning "Unele teste backend eșuează - necesită atenție"
}

Set-Location ".."

Write-Host "\n🧪 Rulează testele frontend..."
Set-Location "frontend"
npm run test -- --run 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Success "Testele frontend trec"
} else {
    Write-Warning "Unele teste frontend eșuează - necesită atenție"
}

Set-Location ".."

# 1.7 Verifică linting
Write-Step "VERIFICARE CALITATE COD" "Yellow"

Write-Host "\n🔍 Verifică linting backend..."
Set-Location "backend"
npm run lint 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Success "Linting backend OK"
} else {
    Write-Warning "Probleme de linting în backend - rulează 'npm run lint:fix'"
}

Set-Location ".."

Write-Host "\n🔍 Verifică linting frontend..."
Set-Location "frontend"
npm run lint 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Success "Linting frontend OK"
} else {
    Write-Warning "Probleme de linting în frontend - rulează 'npm run lint:fix'"
}

Set-Location ".."

# SUMAR FINAL
Write-Step "SUMAR VERIFICARE" "Green"

Write-Host "\n📊 REZULTATE VERIFICARE:" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

$issues = @()

# Verifică din nou toate componentele
if (-not (Test-Path "backend\.env")) { $issues += "❌ backend/.env lipsește" }
if (-not (Test-Path "frontend\.env")) { $issues += "❌ frontend/.env lipsește" }
if (-not (Test-Path "backend\node_modules")) { $issues += "❌ Dependențe backend lipsă" }
if (-not (Test-Path "frontend\node_modules")) { $issues += "❌ Dependențe frontend lipsă" }
if (-not (Test-Path "backend\dist")) { $issues += "⚠️ Build backend nu a reușit" }
if (-not (Test-Path "frontend\dist")) { $issues += "⚠️ Build frontend nu a reușit" }

if ($issues.Count -eq 0) {
    Write-Host "\n🎉 TOATE VERIFICĂRILE AU TRECUT!" -ForegroundColor Green
    Write-Host "✅ Aplicația este gata pentru dezvoltare" -ForegroundColor Green
    Write-Host "\n📋 URMĂTORII PAȘI:" -ForegroundColor Cyan
    Write-Host "1. Rulează 'npm run dev' în backend" -ForegroundColor White
    Write-Host "2. Rulează 'npm run dev' în frontend" -ForegroundColor White
    Write-Host "3. Deschide http://localhost:5173 în browser" -ForegroundColor White
    Write-Host "4. Testează funcționalitățile principale" -ForegroundColor White
} else {
    Write-Host "\n⚠️ PROBLEME IDENTIFICATE:" -ForegroundColor Yellow
    foreach ($issue in $issues) {
        Write-Host $issue -ForegroundColor Red
    }
    Write-Host "\n📋 ACȚIUNI NECESARE:" -ForegroundColor Cyan
    Write-Host "1. Rezolvă problemele de mai sus" -ForegroundColor White
    Write-Host "2. Rulează din nou scriptul pentru verificare" -ForegroundColor White
    Write-Host "3. Consultă documentația pentru detalii" -ForegroundColor White
}

Write-Host "\n📚 DOCUMENTAȚIE:" -ForegroundColor Cyan
Write-Host "- Plan complet: docs/PLAN_IMPLEMENTARE_FUNCTIONALA.md" -ForegroundColor White
Write-Host "- Probleme identificate: docs/PROBLEME_IDENTIFICATE_GHID_IMPLEMENTARE.md" -ForegroundColor White
Write-Host "- Setup dezvoltare: docs/01-SETUP-DEZVOLTARE.md" -ForegroundColor White

Write-Host "\n🏁 VERIFICARE COMPLETĂ!" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green