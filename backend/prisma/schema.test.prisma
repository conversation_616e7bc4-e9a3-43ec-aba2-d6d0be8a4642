generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client-test"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  email                          String              @unique
  password                       String
  avatar                         String?
  role                           String              @default("user")
  currency                       String              @default("USD")
  timezone                       String              @default("UTC")
  isActive                       Boolean             @default(true)
  emailVerified                  Boolean             @default(false)
  emailVerificationToken         String?
  passwordResetToken             String?
  passwordResetExpires           DateTime?
  lastLogin                      DateTime?
  loginCount                     Int                 @default(0)
  preferences                    String?             @default("{\"theme\": \"light\", \"dashboard\": {\"show_trends\": true, \"default_period\": \"month\", \"show_categories\": true}, \"notifications\": {\"push\": false, \"email\": true, \"monthly_report\": true, \"weekly_summary\": true}}")
  createdAt                      DateTime            @default(now())
  updatedAt                      DateTime            @updatedAt
  lastUsageReset                 DateTime            @default(now())
  monthlyExpenseCount            Int                 @default(0)
  monthlyExpenseLimit            Int                 @default(50)
  planType                       String              @default("free")
  stripeCustomerId               String?             @unique
  subscriptionCurrentPeriodEnd   DateTime?
  subscriptionCurrentPeriodStart DateTime?
  subscriptionId                 String?             @unique
  subscriptionStatus             String?
  trialEndsAt                    DateTime?
  refreshToken                   String?
  firstName                      String
  lastName                       String
  id                             String              @id @default(cuid())
  new_id                         String?
  categories                     Category[]
  expenses                       Expense[]
  subscriptions                  Subscription?
  usageLogs                      UsageLog[]          @relation("UserUsageLogs")

  @@map("users")
}

model Category {
  name         String
  description  String?
  color        String       @default("#3B82F6")
  icon         String       @default("shopping-bag")
  budgetLimit  Real?
  budgetPeriod String       @default("monthly")
  isActive     Boolean      @default(true)
  isDefault    Boolean      @default(false)
  sortOrder    Int          @default(0)
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  id           String       @id @default(cuid())
  userId       String
  new_id       String?
  new_user_id  String?
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  expenses     Expense[]

  @@unique([userId, name])
  @@map("categories")
}

model Expense {
  amount                  Real
  description             String
  date                    DateTime
  notes                   String?
  paymentMethod           String              @default("card")
  location                String?
  receiptUrl              String?
  tags                    String?             @default("[]")
  isRecurring             Boolean             @default(false)
  recurringFrequency      String?
  recurringEndDate        DateTime?
  createdAt               DateTime            @default(now())
  updatedAt               DateTime            @updatedAt
  id                      String              @id @default(cuid())
  userId                  String
  categoryId              String
  originalExpenseId       String?
  new_id                  String?
  new_user_id             String?
  new_category_id         String?
  new_original_expense_id String?
  category                Category            @relation(fields: [categoryId], references: [id])
  originalExpense         Expense?            @relation("ExpenseToOriginal", fields: [originalExpenseId], references: [id])
  recurringExpenses       Expense[]           @relation("ExpenseToOriginal")
  user                    User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("expenses")
}

model Plan {
  stripeId      String         @unique
  name          String
  description   String?
  price         Real
  currency      String         @default("USD")
  interval      String
  features      String         @default("{}")
  limits        String         @default("{}")
  isActive      Boolean        @default(true)
  sortOrder     Int            @default(0)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  id            String         @id @default(cuid())
  new_id        String?
  subscriptions Subscription[]

  @@map("plans")
}

model Subscription {
  stripeId           String             @unique
  status             String
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  trialStart         DateTime?
  trialEnd           DateTime?
  canceledAt         DateTime?
  endedAt            DateTime?
  metadata           String?            @default("{}")
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  id                 String             @id @default(cuid())
  userId             String             @unique
  planId             String
  new_id             String?
  new_user_id        String?
  new_plan_id        String?
  plan               Plan               @relation(fields: [planId], references: [id])
  user               User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model UsageLog {
  action          String
  resource        String
  metadata        String?  @default("{}")
  createdAt       DateTime @default(now())
  id              String   @id @default(cuid())
  userId          String
  resourceId      String?
  new_id          String?
  new_user_id     String?
  new_resource_id String?
  user            User     @relation("UserUsageLogs", fields: [userId], references: [id], onDelete: Cascade)

  @@map("usage_logs")
}

model WebhookEvent {
  stripeId    String    @unique
  type        String
  data        String
  processed   Boolean   @default(false)
  processedAt DateTime?
  error       String?
  retryCount  Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  id          String    @id @default(cuid())
  new_id      String?

  @@map("webhook_events")
}