// Backend Types pentru Expense Tracker
import { Request, Response } from 'express';
import { JwtPayload } from 'jsonwebtoken';

// Extended Request interface pentru autentificare
export interface AuthenticatedRequest extends Request {
  user?: User;
}

// JWT Payload customizat
export interface CustomJwtPayload extends JwtPayload {
  userId: string;
  email: string;
}

// Error handling
export interface ApiError extends Error {
  statusCode: number;
  isOperational: boolean;
}

// Subscription usage tracking
export interface SubscriptionUsage {
  userId: string;
  feature: string;
  count: number;
  limit: number;
  resetDate: Date;
}

// Stripe webhook events
export interface StripeWebhookEvent {
  id: string;
  type: string;
  data: {
    object: unknown;
  };
}

// Database models (matching Prisma schema)
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  avatar?: string;
  role: 'user' | 'admin';
  currency: string;
  timezone: string;
  isActive: boolean;
  emailVerified: boolean;
  emailVerificationToken?: string;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  lastLogin?: Date;
  loginCount: number;
  preferences: Record<string, unknown>;
  lastUsageReset: Date;
  monthlyExpenseCount: number;
  monthlyExpenseLimit: number;
  planType: 'free' | 'basic' | 'premium';
  stripeCustomerId?: string;
  subscriptionCurrentPeriodEnd?: Date;
  subscriptionCurrentPeriodStart?: Date;
  subscriptionId?: string;
  subscriptionStatus?: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';
  trialEndsAt?: Date;
  refreshToken?: string;
  createdAt: Date;
  updatedAt: Date;
  subscription?: Subscription;
  expenses: Expense[];
  categories: Category[];
  usageLogs: UsageLog[];
}

export interface Expense {
  id: string;
  amount: number;
  description: string;
  date: Date;
  notes?: string;
  paymentMethod: 'cash' | 'card' | 'bank_transfer' | 'digital_wallet' | 'other';
  location?: string;
  receiptUrl?: string;
  tags: string[];
  isRecurring: boolean;
  recurringFrequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  recurringEndDate?: Date;
  originalExpenseId?: string;
  userId: string;
  categoryId: string;
  createdAt: Date;
  updatedAt: Date;
  user: User;
  category: Category;
  originalExpense?: Expense;
  recurringExpenses: Expense[];
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  budgetLimit?: number;
  budgetPeriod: 'daily' | 'weekly' | 'monthly' | 'yearly';
  isActive: boolean;
  isDefault: boolean;
  sortOrder: number;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  user: User;
  expenses: Expense[];
}

export interface Subscription {
  id: string;
  userId: string;
  stripeCustomerId: string;
  stripeSubscriptionId?: string;
  planId: string;
  status: 'active' | 'canceled' | 'past_due' | 'incomplete';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  createdAt: Date;
  updatedAt: Date;
  user: User;
  plan: SubscriptionPlan;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  stripePriceId: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  subscriptions: Subscription[];
}

// Tipuri pentru Usage Logs
export interface UsageLog {
  id: string;
  userId: string;
  feature: string;
  count: number;
  date: Date;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
  user: User;
}

// Tipuri pentru Webhook Events
export interface WebhookEvent {
  id: string;
  stripeId: string;
  type: string;
  data: Record<string, unknown>;
  processed: boolean;
  processedAt?: Date;
  error?: string;
  retryCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Request/Response DTOs
export interface CreateExpenseDto {
  amount: number;
  description: string;
  categoryId: string;
  date: string;
  notes?: string;
  paymentMethod?: 'cash' | 'card' | 'bank_transfer' | 'digital_wallet' | 'other';
  location?: string;
  receiptUrl?: string;
  tags?: string[];
  isRecurring?: boolean;
  recurringFrequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  recurringEndDate?: string;
}

export interface UpdateExpenseDto {
  amount?: number;
  description?: string;
  categoryId?: string;
  date?: string;
  notes?: string;
  paymentMethod?: 'cash' | 'card' | 'bank_transfer' | 'digital_wallet' | 'other';
  location?: string;
  receiptUrl?: string;
  tags?: string[];
  isRecurring?: boolean;
  recurringFrequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  recurringEndDate?: string;
}

export interface CreateCategoryDto {
  name: string;
  description?: string;
  color: string;
  icon: string;
  budgetLimit?: number;
  budgetPeriod?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  isDefault?: boolean;
  sortOrder?: number;
}

export interface UpdateCategoryDto {
  name?: string;
  description?: string;
  color?: string;
  icon?: string;
  budgetLimit?: number;
  budgetPeriod?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  isActive?: boolean;
  isDefault?: boolean;
  sortOrder?: number;
}

export interface RegisterDto {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  currency?: string;
  timezone?: string;
}

export interface LoginDto {
  email: string;
  password: string;
}

// Query parameters
export interface ExpenseQueryParams {
  page?: number;
  limit?: number;
  categoryId?: string;
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: number;
  sortBy?: 'date' | 'amount' | 'description';
  sortOrder?: 'asc' | 'desc';
}

// Statistics types
export interface ExpenseStats {
  totalExpenses: number;
  monthlyTotal: number;
  categoryBreakdown: {
    categoryId: string;
    categoryName: string;
    total: number;
    count: number;
    percentage: number;
  }[];
  monthlyTrend: {
    month: string;
    year: number;
    total: number;
    count: number;
  }[];
}

// Middleware types
export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

export interface RateLimitInfo {
  limit: number;
  current: number;
  remaining: number;
  resetTime: Date;
}

// Service layer types
export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export interface NotificationData {
  userId: string;
  type: 'email' | 'push';
  title: string;
  message: string;
  data?: Record<string, unknown>;
}

// Configuration types
export interface DatabaseConfig {
  url: string;
  maxConnections: number;
  timeout: number;
}

export interface JwtConfig {
  secret: string;
  expiresIn: string;
  refreshExpiresIn: string;
}

export interface StripeConfig {
  secretKey: string;
  publishableKey: string;
  webhookSecret: string;
  successUrl: string;
  cancelUrl: string;
}

// Utility types
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type Partial<T> = { [P in keyof T]?: T[P] };
export type Required<T> = { [P in keyof T]-?: T[P] };

// Controller response helpers
export interface ControllerResponse<T = any> {
  status: number;
  data?: T;
  message?: string;
  error?: string;
}

// Async handler type
export type AsyncHandler = (
  req: AuthenticatedRequest,
  res: Response,
  next: Function
) => Promise<unknown>;