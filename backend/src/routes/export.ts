import express from 'express';
import { authenticateToken } from '../middleware/auth';
import { checkExportPermission } from '../middleware/subscriptionLimits';
import { exportController } from '../controllers/exportController';

const router = express.Router();

/**
 * Rute pentru export de date
 * Toate rutele necesită autentificare și verificarea permisiunilor de export
 */

// Toate rutele necesită autentificare
router.use(authenticateToken);

/**
 * @route   GET /api/export/csv
 * @desc    Export cheltuieli în format CSV
 * @access  Private (Basic/Premium)
 */
router.get('/csv', checkExportPermission('csv'), exportController.exportCSV);

/**
 * @route   GET /api/export/pdf
 * @desc    Export cheltuieli în format PDF
 * @access  Private (Premium)
 */
router.get('/pdf', checkExportPermission('pdf'), exportController.exportPDF);

/**
 * @route   GET /api/export/excel
 * @desc    Export cheltuieli în format Excel
 * @access  Private (Premium)
 */
router.get('/excel', checkExportPermission('excel'), exportController.exportExcel);

/**
 * @route   GET /api/export/json
 * @desc    Export cheltuieli în format JSON
 * @access  Private (Basic/Premium)
 */
router.get('/json', checkExportPermission('json'), exportController.exportJSON);

/**
 * @route   GET /api/export/stats
 * @desc    Obține statistici pentru export
 * @access  Private (Basic/Premium)
 */
router.get('/stats', checkExportPermission('csv'), exportController.getExportStats);

export default router;