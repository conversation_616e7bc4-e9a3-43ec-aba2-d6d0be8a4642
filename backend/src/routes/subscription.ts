import express, { Request, Response, NextFunction } from 'express';
import { body, param, query } from 'express-validator';
import subscriptionController from '../controllers/subscriptionController';
import { webhookController } from '../controllers/webhookController';
import * as authMiddleware from '../middleware/auth';
import { AuthenticatedRequest } from '../middleware/auth';
import { safeLog } from '../utils/safeLogger';

const router = express.Router();

// Validări pentru request-uri
const validateCheckoutSession = [
  body('planId').notEmpty().withMessage('Plan ID is required').isUUID().withMessage('Plan ID must be a valid UUID'),
];

const validateSessionId = [
  param('sessionId')
    .notEmpty()
    .withMessage('Session ID is required')
    .isString()
    .withMessage('Session ID must be a string'),
];

const validateAction = [
  param('action')
    .notEmpty()
    .withMessage('Action is required')
    .isIn(['createExpense', 'exportData', 'advancedReports'])
    .withMessage('Invalid action'),
];

const validateDateRange = [
  query('startDate').optional().isISO8601().withMessage('Start date must be a valid ISO 8601 date'),
  query('endDate').optional().isISO8601().withMessage('End date must be a valid ISO 8601 date'),
];

// Rute publice (fără autentificare)

/**
 * @route GET /api/subscriptions/plans
 * @desc Obține toate planurile disponibile
 * @access Public
 */
router.get('/plans', subscriptionController.getPlans);

/**
 * @route POST /api/subscriptions/webhook
 * @desc Webhook pentru evenimente Stripe
 * @access Public (dar verificat prin semnătură)
 */
router.post('/webhook', express.raw({ type: 'application/json' }), webhookController.handleStripeWebhook);

// Rute protejate (necesită autentificare)
router.use(authMiddleware.authenticateToken);

/**
 * @route GET /api/subscriptions/current
 * @desc Obține abonamentul curent al utilizatorului
 * @access Private
 */
router.get('/current', subscriptionController.getCurrentSubscription);

/**
 * @route POST /api/subscriptions/checkout
 * @desc Creează o sesiune de checkout pentru abonament
 * @access Private
 */
router.post('/checkout', validateCheckoutSession, subscriptionController.createCheckoutSession);

/**
 * @route POST /api/subscriptions/portal
 * @desc Creează un portal pentru gestionarea abonamentului
 * @access Private
 */
router.post('/portal', subscriptionController.createCustomerPortal);

/**
 * @route POST /api/subscriptions/cancel
 * @desc Anulează abonamentul curent
 * @access Private
 */
router.post('/cancel', subscriptionController.cancelSubscription);

/**
 * @route POST /api/subscriptions/reactivate
 * @desc Reactivează un abonament anulat
 * @access Private
 */
router.post('/reactivate', subscriptionController.reactivateSubscription);

/**
 * @route GET /api/subscriptions/checkout/:sessionId
 * @desc Verifică statusul unei sesiuni de checkout
 * @access Private
 */
router.get('/checkout/:sessionId', validateSessionId, subscriptionController.checkCheckoutSession);

/**
 * @route GET /api/subscriptions/usage
 * @desc Obține statistici de utilizare pentru utilizatorul curent
 * @access Private
 */
router.get('/usage', validateDateRange, subscriptionController.getUsageStats);

/**
 * @route GET /api/subscriptions/permissions/:action
 * @desc Verifică dacă utilizatorul poate efectua o acțiune
 * @access Private
 */
router.get('/permissions/:action', validateAction, subscriptionController.checkPermission);

// Rute pentru admin
router.use('/admin', authMiddleware.requireAdmin);

/**
 * @route POST /api/subscriptions/admin/sync-plans
 * @desc Sincronizează planurile din Stripe (admin only)
 * @access Private (Admin)
 */
router.post('/admin/sync-plans', subscriptionController.syncPlans);

/**
 * @route GET /api/subscriptions/admin/stats
 * @desc Obține statistici despre abonamente (admin only)
 * @access Private (Admin)
 */
router.get('/admin/stats', validateDateRange, subscriptionController.getSubscriptionStats);

/**
 * @route GET /api/subscriptions/admin/webhooks
 * @desc Obține statistici despre webhook-uri (admin only)
 * @access Private (Admin)
 */
router.get('/admin/webhooks', validateDateRange, webhookController.getWebhookStats);

// Rute pentru testare (doar în development)
if (process.env['NODE_ENV'] === 'development') {
  /**
   * @route POST /api/subscriptions/dev/simulate-webhook
   * @desc Simulează un webhook pentru testare
   * @access Private (Development only)
   */
  router.post('/dev/simulate-webhook', async (req: Request, res: Response) => {
    try {
      const { _eventType, _data } = req.body;

      if (!eventType || !data) {
        return res.status(400).json({
          success: false,
          message: 'Event type and data are required',
        });
      }

      // Simulează un eveniment webhook
      const mockEvent = {
        id: `evt_test_${Date.now()}`,
        type: eventType,
        data: { object: data },
        created: Math.floor(Date.now() / 1000),
      };

      await webhookController.processWebhookEvent(mockEvent);

      return res.json({
        success: true,
        message: 'Webhook simulated successfully',
        event: mockEvent,
      });
    } catch (error: unknown) {
      return res.status(500).json({
        success: false,
        message: 'Failed to simulate webhook',
        error: (error as Error).message,
      });
    }
  });

  /**
   * @route GET /api/subscriptions/dev/user-info
   * @desc Obține informații detaliate despre utilizatorul curent pentru debugging
   * @access Private (Development only)
   */
  router.get('/dev/user-info', (req: AuthenticatedRequest, res: Response) => {
    return res.json({
      success: true,
      data: {
        user: {
          id: req.user!.id,
          email: req.user!.email,
          role: req.user!.role,
          stripeCustomerId: (req.user as unknown)['stripeCustomerId'],
      planType: (req.user as unknown)['planType'],
      subscriptionStatus: (req.user as unknown)['subscriptionStatus'],
        },
        subscription: (req as unknown).userSubscription,
        usage: (req as unknown).userUsage,
      },
    });
  });
}

// Middleware pentru gestionarea erorilor specifice rutelor de abonament
router.use((error: unknown, _req: Request, res: Response, _next: NextFunction) => {
  safeLog.error('Subscription route error:', error as Error);

  // Erori specifice Stripe
  if ((error as unknown).type && (error as unknown).type.startsWith('Stripe')) {
    return res.status(400).json({
      success: false,
      message: 'Payment processing error',
      code: (error as unknown).code,
      details: process.env['NODE_ENV'] === 'development' ? (error as Error).message : undefined,
    });
  }

  // Erori de validare
  if ((error as unknown).name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: (error as unknown).errors,
    });
  }

  // Erori generale
  return res.status(500).json({
    success: false,
    message: 'Internal server error',
    details: process.env['NODE_ENV'] === 'development' ? (error as Error).message : undefined,
  });
});

export default router;
