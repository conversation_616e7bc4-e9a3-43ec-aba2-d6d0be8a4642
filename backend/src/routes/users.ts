import express, { Response } from 'express';
import { body, validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import { prisma } from '../config/prisma';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { safeLog } from '../utils/safeLogger';

const router = express.Router();

// Get current user profile
router.get('/profile', authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        currency: true,
        timezone: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    
    res.json(user);
  } catch {
    safeLog.error('Get profile error:', error as Error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user profile
router.put('/profile', [
  authenticateToken,
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('currency')
    .optional()
    .isIn(['RON', 'EUR', 'USD', 'GBP'])
    .withMessage('Currency must be one of: RON, EUR, USD, GBP'),
  body('timezone')
    .optional()
    .isString()
    .withMessage('Timezone must be a valid string')
], async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ 
        error: 'Validation failed', 
        details: errors.array() 
      });
      return;
    }

    const { _firstName, _lastName, _email, _currency, _timezone } = req.body;
    
    // Check if email is already taken by another user
    if (email) {
      const existingUser = await prisma.user.findFirst({ 
        where: { 
          email,
          NOT: {
            id: req.user!.id
          }
        } 
      });
      
      if (existingUser) {
        res.status(400).json({ error: 'Email already in use' });
        return;
      }
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user!.id }
    });
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    // Update user fields
    const updateData: unknown = {};
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (email !== undefined) updateData.email = email;
    if (currency !== undefined) updateData.currency = currency;
    if (timezone !== undefined) updateData.timezone = timezone;

    await prisma.user.update({
      where: { id: req.user!.id },
      data: updateData
    });

    // Return updated user without password
    const updatedUser = await prisma.user.findUnique({
      where: { id: req.user!.id },
      select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          currency: true,
          timezone: true,
          createdAt: true,
          updatedAt: true
        }
    });

    res.json({
      message: 'Profile updated successfully',
      user: updatedUser
    });
  } catch {
    safeLog.error('Update profile error:', error as Error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Change password
router.put('/password', [
  authenticateToken,
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('New password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number')
], async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ 
        error: 'Validation failed', 
        details: errors.array() 
      });
      return;
    }

    const { _currentPassword, _newPassword } = req.body;
    
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id }
    });
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      res.status(400).json({ error: 'Current password is incorrect' });
      return;
    }

    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await prisma.user.update({
      where: { id: req.user!.id },
      data: { password: hashedNewPassword }
    });

    res.json({ message: 'Password updated successfully' });
  } catch {
    safeLog.error('Change password error:', error as Error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete user account
router.delete('/account', authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id }
    });
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    // Delete user (this will cascade delete related expenses and categories)
    await prisma.user.delete({
      where: { id: req.user!.id }
    });

    res.json({ message: 'Account deleted successfully' });
  } catch {
    safeLog.error('Delete account error:', error as Error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user statistics
router.get('/stats', authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user!.id;
    
    // Get total expenses count
    const totalExpenses = await prisma.expense.count({
      where: { userId }
    });
    
    // Get total amount spent
    const totalAmountResult = await prisma.expense.aggregate({
      where: { userId },
      _sum: { amount: true }
    });
    const totalAmount = totalAmountResult._sum?.amount || 0;
    
    // Get categories count
    const categoriesCount = await prisma.category.count({
      where: { userId }
    });
    
    // Get current month expenses
    const currentMonth = new Date();
    const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
    
    const monthlyExpensesResult = await prisma.expense.aggregate({
      where: {
        userId,
        date: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      },
      _sum: { amount: true }
    });
    const monthlyExpenses = monthlyExpensesResult._sum?.amount || 0;
    
    res.json({
      totalExpenses,
      totalAmount,
      categoriesCount,
      monthlyExpenses,
      currency: (req.user as unknown)['currency'] || 'USD'
    });
  } catch {
    safeLog.error('Get user stats error:', error as Error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;