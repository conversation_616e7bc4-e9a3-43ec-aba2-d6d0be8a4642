import { validationResult } from 'express-validator';
import { Request, Response } from 'express';
import { prisma } from '../config/prisma';
import stripeService from '../services/stripeService';
import subscriptionService from '../services/subscriptionService';
import usageService from '../services/usageService';
import logger from '../utils/logger';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    stripeCustomerId?: string;
  };
}

class SubscriptionController {
  /**
   * Obține planurile disponibile
   */
  async getPlans(_req: Request, res: Response): Promise<Response> {
    try {
      const plans = await subscriptionService.getAvailablePlans();

      return res.json({
        success: true,
        data: plans,
      });
    } catch {
      logger.error('Error getting plans:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get plans',
      });
    }
  }

  /**
   * Obține abonamentul curent al utilizatorului
   */
  async getCurrentSubscription(req: Request, res: Response): Promise<Response> {
    try {
      const userId = (req as unknown).user.id;

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          planType: true,
          subscriptionStatus: true,
          subscriptionId: true,
          subscriptionCurrentPeriodStart: true,
          subscriptionCurrentPeriodEnd: true,
          monthlyExpenseCount: true,
          monthlyExpenseLimit: true,
          subscriptions: {
            include: {
              plan: true,
            },
          },
        },
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
        });
      }

      // Get plan from subscription or default to free
      const plan =
        (user.subscriptions && Array.isArray(user.subscriptions) && user.subscriptions.length > 0)
          ? user.subscriptions[0]?.plan
          : await prisma.plan.findFirst({
              where: { stripeId: 'free_plan' },
            });

      if (!plan) {
        return res.status(500).json({
          success: false,
          message: 'No plan found',
        });
      }

      const permissions = plan.features;
      const usageStats = await usageService.getUserUsageStats(userId);

      return res.json({
        success: true,
        data: {
          plan: {
            id: plan.id,
            name: plan.name,
            description: plan.description,
            price: plan.price,
            currency: plan.currency,
            interval: plan.interval,
          },
          subscriptionStatus: user.subscriptionStatus,
          subscriptionId: user.subscriptionId,
          currentPeriodStart: user.subscriptionCurrentPeriodStart,
          currentPeriodEnd: user.subscriptionCurrentPeriodEnd,
          usage: usageStats.currentPeriod,
          permissions,
          limits: plan.limits,
        },
      });
    } catch {
      logger.error('Error getting current subscription:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get subscription',
      });
    }
  }

  /**
   * Creează o sesiune de checkout pentru abonament
   */
  async createCheckoutSession(req: Request, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { planId } = req.body;
      const userId = (req as unknown).user.id;
      // const userEmail = (req as unknown).user.email; // Not used currently

      // Verifică dacă planul există
      const selectedPlan = await prisma.plan.findUnique({
        where: { id: planId },
      });

      if (!selectedPlan || !selectedPlan.stripeId || selectedPlan.stripeId === 'free_plan') {
        return res.status(400).json({
          success: false,
          message: 'Invalid plan selected',
        });
      }

      // Verifică dacă utilizatorul are deja un abonament activ
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { subscriptionStatus: true, email: true },
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
        });
      }

      if (user.subscriptionStatus === 'active') {
        return res.status(400).json({
          success: false,
          message: 'User already has an active subscription',
        });
      }

      // Creează sesiunea de checkout Stripe
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      const session = await stripe.checkout.sessions.create({
        customerEmail: user.email,
        paymentMethodTypes: ['card'],
        lineItems: [
          {
            price: selectedPlan.stripeId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        successUrl: `${process.env.FRONTEND_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${process.env.FRONTEND_URL}/subscription/cancel`,
        metadata: {
          userId,
          planId,
        },
      });

      return res.json({
        success: true,
        data: {
          sessionId: session.id,
          url: session.url,
        },
      });
    } catch {
      logger.error('Error creating checkout session:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to create checkout session',
      });
    }
  }

  /**
   * Creează un portal pentru gestionarea abonamentului
   */
  async createCustomerPortal(req: Request, res: Response) {
    try {
      const userId = (req as unknown).user.id;
      
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
           stripeCustomerId: true,
           planType: true,
           subscriptionStatus: true,
         },
      });

      if (!user || !user.stripeCustomerId) {
        return res.status(400).json({
          success: false,
          message: 'No Stripe customer found for this user',
        });
      }

      const portalSession = await stripeService.createCustomerPortal({
        customerId: user.stripeCustomerId,
        returnUrl: `${process.env.FRONTEND_URL}/subscription`,
      });

      return res.json({
        success: true,
        data: {
          url: portalSession.url,
        },
      });
    } catch {
      logger.error('Error creating customer portal:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to create customer portal',
      });
    }
  }

  /**
   * Anulează abonamentul curent
   */
  async cancelSubscription(req: Request, res: Response) {
    try {
      const userId = (req as unknown).user.id;

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          subscriptionId: true,
          subscriptionStatus: true,
        },
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
        });
      }

      if (!user.subscriptionId || user.subscriptionStatus !== 'active') {
        return res.status(400).json({
          success: false,
          message: 'No active subscription found',
        });
      }

      // Anulează abonamentul în Stripe
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      if (!user || !user.subscriptionId) {
        return res.status(400).json({
          success: false,
          message: 'No active subscription found',
        });
      }

      const canceledSubscription = await stripe.subscriptions.update(user.subscriptionId, {
        cancelAtPeriodEnd: true,
      });

      // Actualizează abonamentul în baza de date
      await prisma.user.update({
        where: { id: userId },
        data: {
          subscriptionStatus: 'canceled',
        },
      });

      await prisma.subscription.updateMany({
        where: { userId },
        data: {
          status: 'canceled',
          canceledAt: new Date(),
        },
      });

      return res.json({
        success: true,
        message: 'Subscription will be canceled at the end of the current period',
        data: {
          subscription: canceledSubscription,
        },
      });
    } catch {
      logger.error('Error canceling subscription:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to cancel subscription',
      });
    }
  }

  /**
   * Reactivează un abonament anulat
   */
  async reactivateSubscription(req: Request, res: Response) {
    try {
      const userId = (req as unknown).user.id;
      // Get user subscription from database
      const subscription = await prisma.subscription.findFirst({
        where: { userId },
        orderBy: { createdAt: 'desc' },
      });

      if (!subscription || subscription.status !== 'canceled') {
        return res.status(400).json({
          success: false,
          message: 'No canceled subscription found',
        });
      }

      // Reactivează abonamentul în Stripe
      const reactivatedSubscription = await stripeService.reactivateSubscription(subscription.stripeId);

      // Actualizează abonamentul în baza de date
      await prisma.subscription.updateMany({
        where: { stripeId: subscription.stripeId },
        data: {
          status: 'active',
          canceledAt: null,
          updatedAt: new Date(),
        },
      });

      return res.json({
        success: true,
        message: 'Subscription reactivated successfully',
        data: {
          subscription: reactivatedSubscription,
        },
      });
    } catch {
      logger.error('Error reactivating subscription:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to reactivate subscription',
      });
    }
  }

  /**
   * Verifică statusul unei sesiuni de checkout
   */
  async checkCheckoutSession(req: Request, res: Response) {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        return res.status(400).json({
          success: false,
          message: 'Session ID is required',
        });
      }

      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      const session = await stripe.checkout.sessions.retrieve(sessionId);

      if (!session) {
        return res.status(404).json({
          success: false,
          message: 'Checkout session not found',
        });
      }

      return res.json({
        success: true,
        data: {
          status: session.paymentStatus,
          customerEmail: session.customerEmail,
          subscriptionId: session.subscription,
        },
      });
    } catch {
      logger.error('Error checking checkout session:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to check checkout session',
      });
    }
  }

  /**
   * Obține statistici de utilizare pentru utilizatorul curent
   */
  async getUsageStats(req: Request, res: Response) {
    try {
      const userId = (req as unknown).user.id;
      const { startDate: _startDate, endDate: _endDate } = req.query;

      const stats = await usageService.getUserUsageStats(userId);

      return res.json({
        success: true,
        data: stats,
      });
    } catch {
      logger.error('Error getting usage stats:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get usage statistics',
      });
    }
  }

  /**
   * Verifică dacă utilizatorul poate efectua o acțiune
   */
  async checkPermission(req: Request, res: Response) {
    try {
      const { action } = req.params;
      const userId = (req as unknown).user.id;

      const canPerform = await subscriptionService.checkUserLimits(userId, action!);
      const usageProgress = await usageService.getUsageProgress(userId);

      return res.json({
        success: true,
        data: {
          canPerform,
          usage: usageProgress,
        },
      });
    } catch {
      logger.error('Error checking permission:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to check permission',
      });
    }
  }

  /**
   * Sincronizează planurile din Stripe (admin only)
   */
  async syncPlans(_req: Request, res: Response): Promise<Response> {
    try {
      await subscriptionService.syncPlansFromStripe();

      return res.json({
        success: true,
        message: 'Plans synced successfully',
      });
    } catch {
      logger.error('Error syncing plans:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to sync plans',
      });
    }
  }

  /**
   * Obține lista abonamentelor cu paginare și filtrare (admin only)
   */
  async getSubscriptions(req: Request, res: Response): Promise<Response> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const {
        page = 1,
        limit = 10,
        search = '',
        status = 'all',
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = req.query;

      // Convert query parameters to proper types
      const pageNum = typeof page === 'string' ? parseInt(_page) : 1;
      const limitNum = typeof limit === 'string' ? parseInt(_limit) : 10;
      const searchStr = typeof search === 'string' ? search : '';
      const statusStr = typeof status === 'string' ? status : 'all';
      const sortByStr = typeof sortBy === 'string' ? sortBy : 'createdAt';
      const sortOrderStr = typeof sortOrder === 'string' ? sortOrder : 'desc';

      const offset = (pageNum - 1) * limitNum;

      // Construiește filtrul
      const where: unknown = {};

      if (statusStr !== 'all') {
        where.status = statusStr;
      }

      if (searchStr) {
        where.OR = [
          {
            user: {
              email: {
                contains: searchStr,
                mode: 'insensitive',
              },
            },
          },
          {
            plan: {
              name: {
                contains: searchStr,
                mode: 'insensitive',
              },
            },
          },
        ];
      }

      // Obține abonamentele cu paginare
      const [subscriptions, total] = await Promise.all([
        prisma.subscription.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
            plan: {
              select: {
                id: true,
                name: true,
                price: true,
                currency: true,
                interval: true,
              },
            },
          },
          orderBy: {
            [sortByStr]: sortOrderStr,
          },
          skip: offset,
          take: limitNum,
        }),
        prisma.subscription.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limitNum);

      return res.json({
        success: true,
        data: {
          subscriptions,
          pagination: {
            currentPage: pageNum,
            totalPages,
            totalItems: total,
            itemsPerPage: limitNum,
            hasNext: pageNum < totalPages,
            hasPrev: pageNum > 1,
          },
        },
      });
    } catch {
      logger.error('Error getting subscriptions:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get subscriptions',
      });
    }
  }

  /**
   * Obține statistici despre abonamente (admin only)
   */
  async getSubscriptionStats(req: Request, res: Response): Promise<Response> {
    try {
      const { _startDate, _endDate } = req.query;

      const subscriptionStats = await subscriptionService.getSubscriptionStats();
      const usageStats = await usageService.getGlobalUsageStats(startDate as string, endDate as string);

      return res.json({
        success: true,
        data: {
          subscriptions: subscriptionStats,
          usage: usageStats,
        },
      });
    } catch {
      logger.error('Error getting subscription stats:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get subscription statistics',
      });
    }
  }

  /**
   * Sincronizează statisticile planurilor (pentru administratori)
   */
  async syncPlanStats(_req: AuthenticatedRequest, res: Response): Promise<Response> {
    try {
      // Obține statistici pentru toate planurile
      const plans = await prisma.plan.findMany({
        include: {
          subscriptions: {
            where: {
              status: 'active',
            },
          },
        },
      });

      const stats = plans.map(plan => ({
        id: plan.id,
        name: plan.name,
        activeSubscriptions: plan.subscriptions.length,
        totalRevenue: plan.subscriptions.length * (Number(plan.price) || 0),
      }));

      return res.json({
        success: true,
        message: 'Plan statistics retrieved successfully',
        data: stats,
      });
    } catch {
      logger.error('Error syncing plan stats:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to sync plan statistics',
      });
    }
  }

  /**
   * Obține statisticile planurilor (pentru administratori)
   */
  async getPlanStats(req: AuthenticatedRequest, res: Response): Promise<Response> {
    try {
      const { startDate: _startDate, endDate: _endDate } = req.query;
      const plans = await prisma.plan.findMany({
        include: {
          subscriptions: {
            where: {
              status: 'active',
            },
          },
          _count: {
            select: {
              subscriptions: true,
            },
          },
        },
      });

      const totalUsers = await prisma.user.count();
      const activeSubscriptions = await prisma.subscription.count({
        where: { status: 'active' },
      });

      const stats = {
        totalUsers,
        activeSubscriptions,
        plans: plans.map(plan => ({
          id: plan.id,
          name: plan.name,
          price: plan.price,
          activeSubscriptions: plan.subscriptions.length,
          totalSubscriptions: plan._count.subscriptions,
          monthlyRevenue: plan.subscriptions.length * (Number(plan.price) || 0),
        })),
      };

      return res.json({
        success: true,
        data: stats,
      });
    } catch {
      logger.error('Error getting plan stats:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get plan statistics',
      });
    }
  }

  /**
   * Gestionează webhook-urile Stripe
   */
  async handleWebhook(req: Request, res: Response): Promise<Response> {
    try {
      const sig = req.headers['stripe-signature'];
      const endpointSecret = process.env['STRIPE_WEBHOOK_SECRET'];

      let event;
      try {
        const stripe = require('stripe')(process.env['STRIPE_SECRET_KEY']);
        event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
      } catch {
        const error = err as Error;
        logger.error('Webhook signature verification failed:', error.message);
        return res.status(400).send(`Webhook Error: ${error.message}`);
      }

      // Înregistrează evenimentul în baza de date
      await prisma.webhookEvent.create({
        data: {
          stripeId: event.id,
          type: event.type,
          processed: false,
          data: event.data,
        },
      });

      // Procesează evenimentul
      switch (event.type) {
        case 'checkout.session.completed':
          await this.handleCheckoutCompleted(event.data.object);
          break;
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object);
          break;
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object);
          break;
        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object);
          break;
        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object);
          break;
        default:
          logger.info(`Unhandled event type: ${event.type}`);
      }

      // Marchează evenimentul ca procesat
      await prisma.webhookEvent.updateMany({
        where: { stripeId: event.id },
        data: { processed: true },
      });

      return res.json({ received: true });
    } catch {
      logger.error('Error handling webhook:', error as Error);
      return res.status(500).json({
        success: false,
        message: 'Webhook processing failed',
      });
    }
  }

  /**
   * Gestionează finalizarea checkout-ului
   */
  async handleCheckoutCompleted(session: unknown) {
    try {
      const { userId } = session.metadata;
      const { planId } = session.metadata;

      if (!userId || !planId) {
        logger.error('Missing metadata in checkout session:', session.id);
        return;
      }

      // Obține informații despre abonament din Stripe
      const stripe = require('stripe')(process.env['STRIPE_SECRET_KEY']);
      const subscription = await stripe.subscriptions.retrieve(session.subscription);
      const plan = await prisma.plan.findUnique({ where: { id: planId } });

      if (!plan) {
        logger.error(`Plan not found: ${planId}`);
        return;
      }

      // Creează abonamentul în baza de date
      await prisma.subscription.create({
        data: {
          user: { connect: { id: userId } },
          plan: { connect: { id: planId } },
          stripeId: subscription.id,
          status: subscription.status,
          currentPeriodStart: new Date(subscription.currentPeriodStart * 1000),
          currentPeriodEnd: new Date(subscription.currentPeriodEnd * 1000),
        },
      });      

      // Actualizează utilizatorul
      await prisma.user.update({
        where: { id: userId },
        data: {
          planType: plan.name.toLowerCase() as 'free' | 'basic' | 'premium',
          subscriptionStatus: subscription.status,
          subscriptionId: subscription.id,
          subscriptionCurrentPeriodStart: new Date(subscription.currentPeriodStart * 1000),
          subscriptionCurrentPeriodEnd: new Date(subscription.currentPeriodEnd * 1000),
          monthlyExpenseLimit: (plan.limits as unknown)?.monthlyExpenses || 1000,
        },
      });

      // TODO: Resetează utilizarea lunară când metoda va fi implementată
      // await usageService.resetMonthlyUsage(userId);

      logger.info(`Subscription created for user ${userId}, plan ${plan.name}`);
    } catch {
      logger.error('Error handling checkout completed:', error as Error);
      throw error;
    }
  }

  /**
   * Gestionează actualizarea abonamentului
   */
  async handleSubscriptionUpdated(subscription: unknown) {
    try {
      // Găsește utilizatorul cu acest abonament
      const user = await prisma.user.findFirst({
        where: { subscriptionId: subscription.id },
      });

      if (!user) {
        logger.error(`User not found for subscription: ${subscription.id}`);
        return;
      }

      // Actualizează abonamentul în baza de date
      await prisma.subscription.updateMany({
          where: { stripeId: subscription.id },
        data: {
          status: subscription.status,
          currentPeriodStart: new Date(subscription.currentPeriodStart * 1000),
          currentPeriodEnd: new Date(subscription.currentPeriodEnd * 1000),
        },
      });      

      // Actualizează utilizatorul
      await prisma.user.update({
        where: { id: user.id },
        data: {
          subscriptionStatus: subscription.status,
          subscriptionCurrentPeriodStart: new Date(subscription.currentPeriodStart * 1000),
          subscriptionCurrentPeriodEnd: new Date(subscription.currentPeriodEnd * 1000),
        },
      });

      // TODO: Dacă abonamentul a fost anulat, retrogradează la planul gratuit
      if (subscription.status === 'canceled') {
        // await subscriptionService.downgradeToFree(user.id);
      }

      logger.info(`Subscription updated for user ${user.id}, status: ${subscription.status}`);
    } catch {
      logger.error('Error handling subscription updated:', error as Error);
      throw error;
    }
  }

  /**
   * Gestionează ștergerea abonamentului
   */
  async handleSubscriptionDeleted(subscription: unknown) {
    try {
      // Găsește utilizatorul cu acest abonament
      const user = await prisma.user.findFirst({
        where: { subscriptionId: subscription.id },
      });

      if (!user) {
        logger.error(`User not found for subscription: ${subscription.id}`);
        return;
      }

      // Actualizează abonamentul în baza de date
      await prisma.subscription.updateMany({
          where: { stripeId: subscription.id },
        data: {
          status: 'canceled',
          canceledAt: new Date(),
        },
      });

      // TODO: Retrogradează utilizatorul la planul gratuit
      // await subscriptionService.downgradeToFree(user.id);

      logger.info(`Subscription deleted for user ${user.id}`);
    } catch {
      logger.error('Error handling subscription deleted:', error as Error);
      throw error;
    }
  }

  /**
   * Gestionează plata reușită
   */
  async handlePaymentSucceeded(invoice: unknown) {
    try {
      const subscriptionId = invoice.subscription;

      if (!subscriptionId) {
        return;
      }

      // Găsește utilizatorul cu acest abonament
      const user = await prisma.user.findFirst({
        where: { subscriptionId },
      });

      if (!user) {
        logger.error(`User not found for subscription: ${subscriptionId}`);
        return;
      }

      // TODO: Resetează utilizarea lunară la începutul unei noi perioade
      // await usageService.resetMonthlyUsage(user.id);

      logger.info(`Payment succeeded for user ${user.id}, subscription ${subscriptionId}`);
    } catch {
      logger.error('Error handling payment succeeded:', error as Error);
      throw error;
    }
  }

  /**
   * Gestionează plata eșuată
   */
  async handlePaymentFailed(invoice: unknown) {
    try {
      const subscriptionId = invoice.subscription;

      if (!subscriptionId) {
        return;
      }

      // Găsește utilizatorul cu acest abonament
      const user = await prisma.user.findFirst({
        where: { subscriptionId },
      });

      if (!user) {
        logger.error(`User not found for subscription: ${subscriptionId}`);
        return;
      }

      // Actualizează statusul abonamentului
      await prisma.subscription.updateMany({
          where: { stripeId: subscriptionId },
        data: {
          status: 'past_due',
        },
      });

      await prisma.user.update({
        where: { id: user.id },
        data: {
          subscriptionStatus: 'past_due',
        },
      });

      logger.info(`Payment failed for user ${user.id}, subscription ${subscriptionId}`);
    } catch {
      logger.error('Error handling payment failed:', error as Error);
      throw error;
    }
  }
}

export default new SubscriptionController();
