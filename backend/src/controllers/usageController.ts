import { Request, Response, NextFunction } from 'express';
import { prisma } from '../config/prisma';
import usageService from '../services/usageService';

class AppError extends Error {
  statusCode: number;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
  }
}

interface AuthenticatedRequest extends Request {
  user?: unknown;
}

/**
 * Controller pentru gestionarea utilizării și limitelor utilizatorilor
 */

/**
 * Obține informații despre utilizarea curentă a utilizatorului
 * GET /api/usage/current
 */
const getCurrentUsage = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = (req.user as unknown)?.id;
    const usageInfo = await usageService.getUserUsageStats(userId.toString());

    res.json({
      success: true,
      data: usageInfo,
    });
  } catch {
    next(error);
  }
};

/**
 * Obține statistici detaliate despre utilizare
 * GET /api/usage/stats
 */
const getUsageStats = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = (req.user as unknown)?.id;
    const { period = 'month' } = req.query;

    // Calculează perioada
    const now = new Date();
    let startDate;

    switch (period) {
      case 'week':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    // Obține statistici cheltuieli
    const expenseStats = await prisma.expense.groupBy({
      by: ['date'],
      where: {
        userId,
        date: {
          gte: startDate,
          lte: now,
        },
      },
      _count: {
        id: true,
      },
      _sum: {
        amount: true,
      },
      orderBy: {
        date: 'asc',
      },
    });

    // Obține statistici categorii
    const categoryStats = await prisma.expense.groupBy({
      by: ['categoryId'],
      where: {
        userId,
        date: {
          gte: startDate,
          lte: now,
        },
      },
      _count: {
        id: true,
      },
      _sum: {
        amount: true,
      },
    });

    // Obține informații despre categorii
    const categories = await prisma.category.findMany({
      where: {
        id: {
          in: categoryStats.map(stat => stat.categoryId),
        },
      },
      select: {
        id: true,
        name: true,
        color: true,
        icon: true,
      },
    });

    // Combină statisticile cu informațiile categoriilor
    const categoryStatsWithInfo = categoryStats.map(stat => {
      const category = categories.find(cat => cat.id === stat.categoryId);
      return {
        ...stat,
        category,
      };
    });

    // Obține informații despre utilizare
    const usageInfo = await usageService.getUserUsageStats(userId.toString());

    res.json({
      success: true,
      data: {
        period,
        startDate,
        endDate: now,
        usage: usageInfo,
        expenses: {
          daily: expenseStats,
          byCategory: categoryStatsWithInfo,
          total: {
            count: expenseStats.reduce((sum: number, stat: unknown) => sum + stat._count.id, 0),
            amount: expenseStats.reduce((sum: number, stat: unknown) => sum + (stat._sum.amount || 0), 0),
          },
        },
      },
    });
  } catch {
    next(error);
  }
};

/**
 * Verifică dacă utilizatorul poate efectua o acțiune specifică
 * POST /api/usage/check-action
 */
const checkActionPermission = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = (req.user as unknown)?.id;
    const { _action, _data } = req.body;

    if (!action) {
      throw new AppError('Acțiunea este obligatorie', 400);
    }

    const usageInfo = await usageService.getUserUsageStats(userId.toString());
    let canPerform = true;
    let reason = null;
    let upgradeRequired = false;

    switch (action) {
      case 'create_expense':
        if (
          usageInfo.limits.expensesPerMonth > 0 &&
          usageInfo.currentPeriod.expenses >= usageInfo.limits.expensesPerMonth
        ) {
          canPerform = false;
          reason = `Ați atins limita de ${usageInfo.limits.expensesPerMonth} cheltuieli pentru planul ${usageInfo.planType.toUpperCase()}`;
          upgradeRequired = true;
        }
        break;

      case 'create_category':
        if (usageInfo.limits.categories > 0 && usageInfo.currentPeriod.categories >= usageInfo.limits.categories) {
          canPerform = false;
          reason = `Ați atins limita de ${usageInfo.limits.categories} categorii personalizate pentru planul ${usageInfo.planType.toUpperCase()}`;
          upgradeRequired = true;
        }
        break;

      case 'export_data': {
        const format = data?.format?.toLowerCase() || 'csv';
        if (usageInfo.planType === 'free' && format !== 'csv') {
          canPerform = false;
          reason = `Formatul ${format.toUpperCase()} nu este disponibil pentru planul ${usageInfo.planType.toUpperCase()}`;
          upgradeRequired = true;
        }
        break;
      }

      case 'access_advanced_reports':
        if (usageInfo.planType === 'free') {
          canPerform = false;
          reason = `Rapoartele avansate nu sunt disponibile pentru planul ${usageInfo.planType.toUpperCase()}`;
          upgradeRequired = true;
        }
        break;

      default:
        throw new AppError('Acțiune necunoscută', 400);
    }

    res.json({
      success: true,
      data: {
        action,
        canPerform,
        reason,
        upgradeRequired,
        currentPlan: usageInfo.planType,
        usage: usageInfo,
      },
    });
  } catch {
    next(error);
  }
};

/**
 * Obține recomandări pentru upgrade bazate pe utilizare
 * GET /api/usage/upgrade-recommendations
 */
const getUpgradeRecommendations = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const usageInfo = await usageService.getUserUsageStats(userId.toString());

    const recommendations = [];

    // Verifică dacă utilizatorul se apropie de limitele planului
    if (usageInfo.planType === 'free') {
      // Recomandări pentru utilizatorii gratuit
      if (usageInfo.usagePercentage.expenses >= 80) {
        recommendations.push({
          type: 'expense_limit',
          priority: 'high',
          title: 'Vă apropiați de limita de cheltuieli',
          description: `Ați folosit ${usageInfo.currentPeriod.expenses} din ${usageInfo.limits.expensesPerMonth} cheltuieli disponibile (${usageInfo.usagePercentage.expenses}%)`,
          suggestedPlan: 'basic',
          benefits: ['500 cheltuieli/lună', 'Export PDF', 'Categorii nelimitate'],
        });
      }

      if (usageInfo.limits.categories > 0 && usageInfo.currentPeriod.categories >= usageInfo.limits.categories * 0.8) {
        recommendations.push({
          type: 'category_limit',
          priority: 'medium',
          title: 'Vă apropiați de limita de categorii',
          description: `Ați creat ${usageInfo.currentPeriod.categories} din ${usageInfo.limits.categories} categorii personalizate disponibile`,
          suggestedPlan: 'basic',
          benefits: ['Categorii nelimitate', 'Export avansat'],
        });
      }

      // Recomandare generală pentru funcționalități premium
      recommendations.push({
        type: 'features',
        priority: 'low',
        title: 'Deblocați funcționalități avansate',
        description: 'Accesați rapoarte avansate, export în multiple formate și multe altele',
        suggestedPlan: 'premium',
        benefits: ['Rapoarte avansate', 'Export Excel', 'Suport prioritar', 'API access'],
      });
    } else if (usageInfo.planType === 'basic') {
      // Recomandări pentru utilizatorii basic
      if (usageInfo.usagePercentage.expenses >= 80) {
        recommendations.push({
          type: 'expense_limit',
          priority: 'high',
          title: 'Vă apropiați de limita de cheltuieli',
          description: `Ați folosit ${usageInfo.currentPeriod.expenses} din ${usageInfo.limits.expensesPerMonth} cheltuieli disponibile (${usageInfo.usagePercentage.expenses}%)`,
          suggestedPlan: 'premium',
          benefits: ['Cheltuieli nelimitate', 'Rapoarte avansate', 'Suport prioritar'],
        });
      }

      recommendations.push({
        type: 'premium_features',
        priority: 'medium',
        title: 'Accesați funcționalități premium',
        description: 'Obțineți rapoarte avansate, predicții și suport prioritar',
        suggestedPlan: 'premium',
        benefits: ['Rapoarte avansate', 'Predicții buget', 'Export Excel', 'API access'],
      });
    }

    res.json({
      success: true,
      data: {
        currentPlan: usageInfo.planType,
        usage: usageInfo,
        recommendations: recommendations.sort((a, b) => {
          const priorityOrder: { [key: string]: number } = { high: 3, medium: 2, low: 1 };
          return (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
        }),
      },
    });
  } catch {
    next(error);
  }
};

export { getCurrentUsage, getUsageStats, checkActionPermission, getUpgradeRecommendations };

export default {
  getCurrentUsage,
  getUsageStats,
  checkActionPermission,
  getUpgradeRecommendations,
};
