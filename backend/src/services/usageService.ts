import { PrismaClient } from '@prisma/client';
import logger from '../utils/logger';

const prisma = new PrismaClient();

interface UsageStats {
  currentPeriod: {
    expenses: number;
    categories: number;
    exports: number;
  };
  limits: {
    expensesPerMonth: number;
    categories: number;
    exportsPerMonth: number;
  };
  usagePercentage: {
    expenses: number;
    categories: number;
    exports: number;
  };
  planType: string;
}

interface PlanLimits {
  expensesPerMonth: number;
  categories: number;
  exportsPerMonth: number;
}

const PLAN_LIMITS: Record<string, PlanLimits> = {
  free: {
    expensesPerMonth: 50,
    categories: 10,
    exportsPerMonth: 3,
  },
  basic: {
    expensesPerMonth: 500,
    categories: 50,
    exportsPerMonth: 20,
  },
  premium: {
    expensesPerMonth: -1, // unlimited
    categories: -1, // unlimited
    exportsPerMonth: -1, // unlimited
  },
};

class UsageService {
  /**
   * Obține statisticile de utilizare pentru un utilizator
   */
  async getUserUsageStats(userId: string): Promise<UsageStats> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          planType: true,
          subscriptionStatus: true,
        },
      });

      if (!user) {
        throw new Error('User not found');
      }

      const planType = user.planType || 'free';
    const limits = PLAN_LIMITS[planType as keyof typeof PLAN_LIMITS] || PLAN_LIMITS['free'];

      // Calculează perioada curentă (luna curentă)
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      // Obține utilizarea curentă
      const [expenseCount, categoryCount, exportCount] = await Promise.all([
        this.getExpenseCount(userId, startOfMonth, endOfMonth),
        this.getCategoryCount(userId),
        this.getExportCount(userId, startOfMonth, endOfMonth),
      ]);

      // Verifică dacă limits există și calculează procentajele de utilizare
      if (!limits) {
        throw new Error('Plan limits not found');
      }

      const usagePercentage = {
      expenses: limits.expensesPerMonth === -1 ? 0 : Math.round((expenseCount / limits.expensesPerMonth) * 100),
      categories: limits.categories === -1 ? 0 : Math.round((categoryCount / limits.categories) * 100),
      exports: limits.exportsPerMonth === -1 ? 0 : Math.round((exportCount / limits.exportsPerMonth) * 100),
    };

      return {
        currentPeriod: {
          expenses: expenseCount,
          categories: categoryCount,
          exports: exportCount,
        },
        limits: {
          expensesPerMonth: limits.expensesPerMonth,
          categories: limits.categories,
          exportsPerMonth: limits.exportsPerMonth,
        },
        usagePercentage,
        planType: planType,
      };
    } catch {
      logger.error('Error getting user usage stats:', error);
      throw error;
    }
  }

  /**
   * Incrementează utilizarea pentru o acțiune specifică
   */
  async incrementUsage(userId: string, action: string): Promise<void> {
    try {
      // Verifică dacă utilizatorul există
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        logger.warn(`User ${userId} not found, skipping usage increment`);
        return;
      }

      // Înregistrează acțiunea în usage_logs
      await prisma.usageLog.create({
        data: {
          userId: userId,
          action,
          resource: this.getResourceFromAction(action),
          createdAt: new Date(),
        },
      });

      logger.info(`Usage incremented for user ${userId}, action: ${action}`);
    } catch {
      logger.error('Error incrementing usage:', error);
      throw error;
    }
  }

  /**
   * Helper method pentru a obține resursa din acțiune
   */
  private getResourceFromAction(action: string): string {
    switch (action) {
      case 'createExpense':
        case 'updateExpense':
        case 'deleteExpense':
          return 'expense';
        case 'createCategory':
        case 'updateCategory':
        case 'deleteCategory':
          return 'category';
        case 'exportData':
          return 'export';
      default:
        return 'unknown';
    }
  }

  /**
   * Obține numărul de cheltuieli pentru o perioadă
   */
  private async getExpenseCount(userId: string, startDate: Date, endDate: Date): Promise<number> {
    return await prisma.expense.count({
      where: {
        userId: userId,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    });
  }

  /**
   * Obține numărul de categorii personalizate
   */
  private async getCategoryCount(userId: string): Promise<number> {
    return await prisma.category.count({
      where: {
        userId: userId,
        isDefault: false,
      },
    });
  }

  /**
   * Obține numărul de export-uri pentru o perioadă
   */
  private async getExportCount(userId: string, startDate: Date, endDate: Date): Promise<number> {
    return await prisma.usageLog.count({
      where: {
        userId: userId,
        action: 'exportData',
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    });
  }

  /**
   * Verifică dacă utilizatorul poate efectua o acțiune
   */
  async canPerformAction(userId: string, action: string): Promise<boolean> {
    try {
      const stats = await this.getUserUsageStats(userId);

      switch (action) {
        case 'createExpense':
        if (stats.limits.expensesPerMonth === -1) return true;
        return stats.currentPeriod.expenses < stats.limits.expensesPerMonth;

      case 'createCategory':
        if (stats.limits.categories === -1) return true;
        return stats.currentPeriod.categories < stats.limits.categories;

      case 'exportData':
        if (stats.limits.exportsPerMonth === -1) return true;
        return stats.currentPeriod.exports < stats.limits.exportsPerMonth;

        default:
          return true;
      }
    } catch {
      logger.error('Error checking if user can perform action:', error);
      return false;
    }
  }

  /**
   * Resetează utilizarea pentru o perioadă
   */
  async resetUsageForPeriod(userId: string, period: string): Promise<void> {
    try {
      const startDate = new Date();
      const endDate = new Date();

      if (period === 'month') {
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
        endDate.setMonth(endDate.getMonth() + 1);
        endDate.setDate(0);
        endDate.setHours(23, 59, 59, 999);
      }

      // Șterge log-urile de utilizare pentru perioada specificată
      await prisma.usageLog.deleteMany({
        where: {
          userId: userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      logger.info(`Usage reset for user ${userId}, period: ${period}`);
    } catch {
      logger.error('Error resetting usage:', error);
      throw error;
    }
  }

  /**
   * Obține statistici globale de utilizare
   */
  async getGlobalUsageStats(startDate?: string, endDate?: string): Promise<unknown> {
    try {
      const start = startDate ? new Date(_startDate) : new Date(new Date().getFullYear(), new Date().getMonth(), 1);
      const end = endDate ? new Date(_endDate) : new Date();

      const [totalUsers, activeUsers, totalExpenses, totalCategories, totalExports] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({
          where: {
            lastLogin: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
        }),
        prisma.expense.count({
          where: {
            createdAt: {
              gte: start,
              lte: end,
            },
          },
        }),
        prisma.category.count({
          where: {
            createdAt: {
              gte: start,
              lte: end,
            },
          },
        }),
        prisma.usageLog.count({
          where: {
            action: 'exportData',
            createdAt: {
              gte: start,
              lte: end,
            },
          },
        }),
      ]);

      return {
        totalUsers: totalUsers,
      activeUsers: activeUsers,
      totalExpenses: totalExpenses,
      totalCategories: totalCategories,
      totalExports: totalExports,
        period: {
          start: start.toISOString(),
          end: end.toISOString(),
        },
      };
    } catch {
      logger.error('Error getting global usage stats:', error);
      throw error;
    }
  }

  /**
   * Obține progresul utilizării pentru un utilizator
   */
  async getUsageProgress(userId: string): Promise<unknown> {
    try {
      const stats = await this.getUserUsageStats(userId);

      return {
        expenses: {
          current: stats.currentPeriod.expenses,
        limit: stats.limits.expensesPerMonth,
        percentage: stats.usagePercentage.expenses,
        unlimited: stats.limits.expensesPerMonth === -1,
        },
        categories: {
          current: stats.currentPeriod.categories,
          limit: stats.limits.categories,
          percentage: stats.usagePercentage.categories,
          unlimited: stats.limits.categories === -1,
        },
        exports: {
          current: stats.currentPeriod.exports,
        limit: stats.limits.exportsPerMonth,
        percentage: stats.usagePercentage.exports,
        unlimited: stats.limits.exportsPerMonth === -1,
        },
        planType: stats.planType,
      };
    } catch {
      logger.error('Error getting usage progress:', error);
      throw error;
    }
  }
}

const usageService = new UsageService();
export default usageService;
