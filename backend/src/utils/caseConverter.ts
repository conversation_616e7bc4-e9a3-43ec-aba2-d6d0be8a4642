/**
 * Utilitare pentru conversie între snake_case și camelCase
 * Folosit pentru transformarea automată a datelor între frontend și backend
 */

// Tipuri pentru conversie
export type CaseConversionType = 'snakeToCamel' | 'camelToSnake';

/**
 * Convertește un string de la snake_case la camelCase
 */
export function snakeToCamel(str: string): string {
  if (!str || typeof str !== 'string') return str;
  
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

/**
 * Convertește un string de la camelCase la snake_case
 */
export function camelToSnake(str: string): string {
  if (!str || typeof str !== 'string') return str;
  
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

/**
 * Convertește cheile unui obiect de la snake_case la camelCase
 */
export function convertObjectKeysToCamel<T = any>(obj: unknown): T {
  if (obj === null || obj === undefined) return obj as T;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertObjectKeysToCamel(item)) as T;
  }
  
  if (typeof obj === 'object' && obj.constructor === Object) {
    const converted: unknown = {};

    for (const [key, value] of Object.entries(obj)) {
      const camelKey = snakeToCamel(key);
      converted[camelKey] = convertObjectKeysToCamel(value);
    }

    return converted as T;
  }

  return obj as T;
}

/**
 * Convertește cheile unui obiect de la camelCase la snake_case
 */
export function convertObjectKeysToSnake<T = any>(obj: unknown): T {
  if (obj === null || obj === undefined) return obj as T;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertObjectKeysToSnake(item)) as T;
  }
  
  if (typeof obj === 'object' && obj.constructor === Object) {
    const converted: unknown = {};

    for (const [key, value] of Object.entries(obj)) {
      const snakeKey = camelToSnake(key);
      converted[snakeKey] = convertObjectKeysToSnake(value);
    }

    return converted as T;
  }

  return obj as T;
}

/**
 * Mapping specific pentru câmpurile aplicației
 * Folosit pentru conversii speciale care nu urmează regula generală
 */
export const FIELD_MAPPING = {
  // Conversii speciale snake_case -> camelCase
  snakeToCamel: {
    'created_at': 'createdAt',
    'updated_at': 'updatedAt',
    'email_verified': 'emailVerified',
    'is_active': 'isActive',
    'is_default': 'isDefault',
    'is_recurring': 'isRecurring',
    'category_id': 'categoryId',
    'user_id': 'userId',
    'expense_id': 'expenseId',
    'plan_id': 'planId',
    'subscription_id': 'subscriptionId',
    'expense_date': 'expenseDate',
    'payment_method': 'paymentMethod',
    'receipt_url': 'receiptUrl',
    'last_login': 'lastLogin',
    'login_count': 'loginCount',
    'password_reset_token': 'passwordResetToken',
    'password_reset_expires': 'passwordResetExpires',
    'email_verification_token': 'emailVerificationToken',
    'subscription_plan': 'planType',
    'subscription_status': 'subscriptionStatus',
    'stripe_customer_id': 'stripeCustomerId',
    'stripe_id': 'stripeId',
    'budget_limit': 'budgetLimit',
    'budget_period': 'budgetPeriod',
    'sort_order': 'sortOrder',
    'recurring_frequency': 'recurringFrequency',
    'recurring_end_date': 'recurringEndDate',
    'original_expense_id': 'originalExpenseId',
    'current_period_start': 'currentPeriodStart',
    'current_period_end': 'currentPeriodEnd',
    'trial_start': 'trialStart',
    'trial_end': 'trialEnd',
    'canceled_at': 'canceledAt',
    'ended_at': 'endedAt',
    'processed_at': 'processedAt',
    'retry_count': 'retryCount',
    'resource_id': 'resourceId',
    'monthly_expense_count': 'monthlyExpenseCount',
    'monthly_expense_limit': 'monthlyExpenseLimit',
    'last_usage_reset': 'lastUsageReset',
    'trial_ends_at': 'trialEndsAt',
    'refresh_token': 'refreshToken',
    'first_name': 'firstName',
    'last_name': 'lastName'
  },
  
  // Conversii inverse camelCase -> snake_case
  camelToSnake: {} as Record<string, string>
};

// Generează mapping-ul invers automat
for (const [snake, camel] of Object.entries(FIELD_MAPPING.snakeToCamel)) {
  FIELD_MAPPING.camelToSnake[camel] = snake;
}

/**
 * Convertește cheile unui obiect folosind mapping-ul specific
 */
export function convertWithMapping<T = any>(
  obj: unknown, 
  direction: CaseConversionType
): T {
  if (obj === null || obj === undefined) return obj as T;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertWithMapping(item, direction)) as T;
  }
  
  if (typeof obj === 'object' && obj.constructor === Object) {
    const converted: unknown = {};
    const mapping = direction === 'snakeToCamel' 
      ? FIELD_MAPPING.snakeToCamel 
      : FIELD_MAPPING.camelToSnake;
    
    for (const [key, value] of Object.entries(obj)) {
      // Folosește mapping-ul specific dacă există, altfel conversie automată
      const convertedKey = (mapping as Record<string, string>)[key] || (
        direction === 'snakeToCamel' ? snakeToCamel(_key) : camelToSnake(key)
      );

      converted[convertedKey] = convertWithMapping(value, direction);
    }
    
    return converted as T;
  }
  
  return obj as T;
}

/**
 * Funcții de conveniență pentru conversii rapide
 */
export const CaseConverter = {
  /**
   * Convertește de la snake_case la camelCase
   */
  toCamel: <T = any>(obj: unknown): T => convertWithMapping<T>(obj, 'snakeToCamel'),
  
  /**
   * Convertește de la camelCase la snake_case
   */
  toSnake: <T = any>(obj: unknown): T => convertWithMapping<T>(obj, 'camelToSnake'),
  
  /**
   * Convertește un string individual
   */
  stringToCamel: snakeToCamel,
  stringToSnake: camelToSnake,
  
  /**
   * Verifică dacă un string este în snake_case
   */
  isSnakeCase: (str: string): boolean => {
    return /^[a-z]+(_[a-z]+)*$/.test(str);
  },
  
  /**
   * Verifică dacă un string este în camelCase
   */
  isCamelCase: (str: string): boolean => {
    return /^[a-z]+([A-Z][a-z]*)*$/.test(str);
  },
  
  /**
   * Detectează automat formatul și convertește la celălalt
   */
  autoConvert: (str: string): string => {
    if (CaseConverter.isSnakeCase(str)) {
      return snakeToCamel(str);
    } else if (CaseConverter.isCamelCase(str)) {
      return camelToSnake(str);
    }
    return str;
  }
};

/**
 * Utilitare pentru debugging și logging
 */
export const CaseConverterUtils = {
  /**
   * Analizează un obiect și returnează statistici despre formatul cheilor
   */
  analyzeObject: (obj: unknown): {
    totalKeys: number;
    snakeCaseKeys: number;
    camelCaseKeys: number;
    mixedKeys: number;
    unknownKeys: number;
  } => {
    const stats = {
      totalKeys: 0,
      snakeCaseKeys: 0,
      camelCaseKeys: 0,
      mixedKeys: 0,
      unknownKeys: 0
    };
    
    const analyzeRecursive = (item: unknown) => {
      if (typeof item === 'object' && item !== null && !Array.isArray(item)) {
        for (const key of Object.keys(item)) {
          stats.totalKeys++;
          
          if (CaseConverter.isSnakeCase(key)) {
            stats.snakeCaseKeys++;
          } else if (CaseConverter.isCamelCase(key)) {
            stats.camelCaseKeys++;
          } else if (key.includes('_') && /[A-Z]/.test(key)) {
            stats.mixedKeys++;
          } else {
            stats.unknownKeys++;
          }
          
          analyzeRecursive((item as unknown)[key]);
        }
      } else if (Array.isArray(item)) {
        item.forEach(analyzeRecursive);
      }
    };
    
    analyzeRecursive(obj);
    return stats;
  },
  
  /**
   * Validează că un obiect folosește consistent un format
   */
  validateConsistency: (obj: unknown, expectedFormat: 'snake' | 'camel'): {
    isConsistent: boolean;
    violations: string[];
  } => {
    const violations: string[] = [];
    
    const validateRecursive = (item: unknown, path: string = '') => {
      if (typeof item === 'object' && item !== null && !Array.isArray(item)) {
        for (const key of Object.keys(item)) {
          const currentPath = path ? `${path}.${key}` : key;
          
          const isExpectedFormat = expectedFormat === 'snake' 
            ? CaseConverter.isSnakeCase(key)
            : CaseConverter.isCamelCase(key);
          
          if (!isExpectedFormat) {
            violations.push(currentPath);
          }
          
          validateRecursive((item as unknown)[key], currentPath);
        }
      } else if (Array.isArray(item)) {
        item.forEach((subItem, index) => {
          validateRecursive(subItem, `${path}[${index}]`);
        });
      }
    };
    
    validateRecursive(obj);
    
    return {
      isConsistent: violations.length === 0,
      violations
    };
  }
};

export default CaseConverter;
