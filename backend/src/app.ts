import express, { Application, Request, Response } from 'express';
import compression from 'compression';
import morgan from 'morgan';
import { config } from 'dotenv';
import { safeLog } from './utils/safeLogger';

safeLog.debug('🚀 Starting application...');
safeLog.debug('📦 Imports loaded successfully');

// Load environment variables
config();
safeLog.debug('🔧 Environment variables loaded');

// Import middleware
safeLog.debug('🔧 Starting middleware imports...');
import { setupSecurity } from './middleware/security';
import { attackDetection } from './middleware/attackDetection';
import { headerSecurity } from './middleware/headerSecurity';
import { botDetection } from './middleware/botDetection';
import { auditLogger, adminAuditLogger } from './middleware/audit';
// import errorHandler from './middleware/errorHandler'; // Auto-removed: potentially unused import
// import notFound from './middleware/notFound'; // Auto-removed: potentially unused import
import {
  userCache,
  expenseCache,
  categoryCache,
  reportCache,
  invalidateUserCache,
  invalidateExpenseCache,
  invalidateCategoryCache
} from './middleware/cache';
safeLog.debug('🛡️ Middleware imported successfully');

// Import routes
safeLog.debug('🛣️ Starting routes import...');
// import authRoutes from './routes/auth'; // Auto-removed: potentially unused import
safeLog.debug('✅ Auth routes imported');
// import userRoutes from './routes/users'; // Auto-removed: potentially unused import
safeLog.debug('✅ User routes imported');
// import categoryRoutes from './routes/categories'; // Auto-removed: potentially unused import
safeLog.debug('✅ Category routes imported');
// import expenseRoutes from './routes/expenses'; // Auto-removed: potentially unused import
safeLog.debug('✅ Expense routes imported');
// import exportRoutes from './routes/export'; // Auto-removed: potentially unused import
safeLog.debug('✅ Export routes imported');
// import subscriptionRoutes from './routes/subscription'; // Auto-removed: potentially unused import
safeLog.debug('✅ Subscription routes imported');
// import adminRoutes from './routes/admin'; // Auto-removed: potentially unused import
safeLog.debug('🛣️ All routes imported successfully');

// Import database utilities
import { initializeDatabase, closeConnection } from './config/prisma';
// import logger from './utils/logger'; // Auto-removed: potentially unused import
safeLog.debug('🗄️ Database utilities imported');

const app: Application = express();
const PORT: number = parseInt(process.env.PORT || '3000', 10);

// Security middleware setup
setupSecurity(app);

// Advanced security middleware
app.use(attackDetection);
app.use(headerSecurity);
app.use(botDetection);

// Compression middleware
app.use(compression());

// Audit logging middleware (before routes)
app.use(auditLogger);

// Logging middleware
if (process.env['NODE_ENV'] !== 'test') {
  app.use(morgan('combined'));
}

// Stripe webhook endpoint (must be before body parsing middleware)
app.use('/api/webhooks/stripe', express.raw({ type: 'application/json' }));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (_req: Request, res: Response) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env['NODE_ENV'] || 'development',
    version: process.env.npm_package_version || '1.0.0',
  });
});

// API routes with cache middleware
app.use('/api/auth', authRoutes);
app.use('/api/users', userCache, invalidateUserCache, userRoutes);
app.use('/api/expenses', expenseCache, invalidateExpenseCache, expenseRoutes);
app.use('/api/categories', categoryCache, invalidateCategoryCache, categoryRoutes);
app.use('/api/export', reportCache, exportRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/admin', adminAuditLogger, adminRoutes);

// API documentation endpoint
app.get('/api', (_req: Request, res: Response) => {
  res.json({
    message: 'Expense Tracker API',
    version: '1.0.0',
    endpoints: {
      auth: {
        'POST /api/auth/register': 'Register new user',
        'POST /api/auth/login': 'Login user',
        'POST /api/auth/refresh': 'Refresh access token',
        'POST /api/auth/logout': 'Logout user',
        'GET /api/auth/profile': 'Get user profile',
      },
      users: {
        'GET /api/users/profile': 'Get current user profile',
        'PUT /api/users/profile': 'Update user profile',
        'DELETE /api/users/account': 'Delete user account',
      },
      expenses: {
        'GET /api/expenses': 'Get user expenses',
        'POST /api/expenses': 'Create new expense',
        'GET /api/expenses/:id': 'Get expense by ID',
        'PUT /api/expenses/:id': 'Update expense',
        'DELETE /api/expenses/:id': 'Delete expense',
        'GET /api/expenses/stats/monthly': 'Get monthly statistics',
        'GET /api/expenses/stats/category': 'Get category statistics',
      },
      categories: {
        'GET /api/categories': 'Get user categories',
        'POST /api/categories': 'Create new category',
        'PUT /api/categories/:id': 'Update category',
        'DELETE /api/categories/:id': 'Delete category',
      },
      subscriptions: {
        'GET /api/subscriptions/plans': 'Get available plans',
        'GET /api/subscriptions/current': 'Get current subscription',
        'POST /api/subscriptions/checkout': 'Create checkout session',
        'POST /api/subscriptions/portal': 'Create customer portal',
        'POST /api/subscriptions/cancel': 'Cancel subscription',
        'POST /api/subscriptions/reactivate': 'Reactivate subscription',
        'GET /api/subscriptions/usage': 'Get usage statistics',
        'POST /api/webhooks/stripe': 'Stripe webhook endpoint',
      },
      admin: {
        'GET /api/admin/dashboard/stats': 'Get admin dashboard statistics',
        'GET /api/admin/alerts': 'Get system alerts',
        'POST /api/admin/alerts/:alertId/read': 'Mark alert as read',
        'GET /api/admin/users': 'Get users list with pagination',
        'GET /api/admin/users/:userId': 'Get user details',
      },
    },
    documentation: 'https://github.com/your-repo/expense-tracker/wiki/API-Documentation',
  });
});

// 404 handler
app.use(notFound);

// Error handling middleware (must be last)
app.use(errorHandler);

// Database connection and server startup
const startServer = async (): Promise<void> => {
  try {
    // Initialize database connection
    await initializeDatabase();
    safeLog.debug('✅ Database connection established successfully.');

    // Start server
    if (process.env['NODE_ENV'] !== 'test') {
      app.listen(PORT, '0.0.0.0', () => {
        safeLog.debug(`🚀 Server running on port ${PORT}`);
        safeLog.debug(`📊 Environment: ${process.env['NODE_ENV'] || 'development'}`);
        safeLog.debug(`🔗 API URL: http://0.0.0.0:${PORT}/api`);
        safeLog.debug(`❤️  Health check: http://0.0.0.0:${PORT}/health`);
      });
    }
  } catch {
    safeLog.error('❌ Unable to start server:', error as Error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err: Error) => {
  logger.error('❌ Unhandled Promise Rejection:', {
    error: err.message,
    stack: err.stack,
    timestamp: new Date().toISOString()
  });
  safeLog.error('❌ Unhandled Promise Rejection:', err);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err: Error) => {
  logger.error('❌ Uncaught Exception:', {
    error: err.message,
    stack: err.stack,
    timestamp: new Date().toISOString()
  });
  safeLog.error('❌ Uncaught Exception:', err);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  safeLog.debug('🔄 SIGTERM received, shutting down gracefully...');
  
  // Close cache connections
  try {
    const { closeCache } = require('./middleware/cache');
    await closeCache();
    logger.info('Cache connections closed successfully');
  } catch {
    logger.error('Error closing cache connections:', error);
  }
  
  // Close database connection
  await closeConnection();
  
  // Logger will be closed automatically
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  safeLog.debug('🔄 SIGINT received, shutting down gracefully...');
  
  // Close cache connections
  try {
    const { closeCache } = require('./middleware/cache');
    await closeCache();
    logger.info('Cache connections closed successfully');
  } catch {
    logger.error('Error closing cache connections:', error);
  }
  
  // Close database connection
  await closeConnection();
  
  // Logger will be closed automatically
  
  process.exit(0);
});

// Start the server
if (require.main === module) {
  startServer();
}

export default app;