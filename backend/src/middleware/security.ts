import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import hpp from 'hpp';
import mongoSanitize from 'express-mongo-sanitize';
import xss from 'xss-clean';
import cors from 'cors';
import { Request, Response, NextFunction, Application } from 'express';
import crypto from 'crypto';
import logger from '../utils/logger';

// Interface for authenticated request
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    [key: string]: unknown;
  };
  id?: string;
}

// Interface for CORS callback
type CorsCallback = (err: Error | null, allow?: boolean) => void;

/**
 * Lista albă de origini permise, încărcată din variabilele de mediu.
 */
const allowedOrigins = process.env.CORS_ALLOWED_ORIGINS?.split(',') || [];

/**
 * Configurare CORS pentru aplicație
 */
const corsOptions: cors.CorsOptions = {
  origin (origin: string | undefined, callback: CorsCallback) {
    // Permite cererile fără origine (ex: Postman, cURL) în afara producției
    if (!origin && process.env['NODE_ENV'] !== 'production') {
      return callback(null, true);
    }

    // Verifică dacă originea este în lista albă
    if (origin && allowedOrigins.includes(origin)) {
      logger.logSecurity('corsRequestAllowed', {
        origin,
        timestamp: new Date().toISOString(),
      });
      return callback(null, true);
    } else {
      logger.logSecurity('corsRequestDenied', {
        origin: origin || 'no-origin',
        timestamp: new Date().toISOString(),
      });
      // Respinge cererea dacă originea nu este permisă
      const msg = `Originea ${origin} nu este permisă de politica CORS.`;
      return callback(new Error(msg));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'X-Request-ID',
  ],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
  maxAge: 86400, // 24 ore
};

/**
 * Configurare Helmet pentru securitate HTTP
 */
const helmetConfig = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      imgSrc: ["'self'", 'data:', 'https:'],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", 'https://api.stripe.com'],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000, // 1 an
    includeSubDomains: true,
    preload: true,
  },
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
};

/**
 * Rate limiting pentru protecția împotriva spam-ului
 */
const createRateLimiter = (windowMs: number, max: number, message: string, skipSuccessfulRequests: boolean = false) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      error: {
        message,
        retryAfter: Math.ceil(windowMs / 1000),
      },
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests,
    handler: (req: AuthenticatedRequest, res: Response) => {
      logger.logSecurity('rateLimitExceeded', {
        ip: req.ip,
        url: req.originalUrl,
        method: req.method,
        userAgent: req.get('User-Agent'),
        userId: req.user?.id,
      });

      res.status(429).json({
        success: false,
        error: {
          message,
          retryAfter: Math.ceil(windowMs / 1000),
        },
      });
    },
  });
};

/**
 * Slow down pentru încetinirea cererilor repetate
 */
const createSlowDown = (windowMs: number, delayAfter: number, delayMs: number) => {
  return slowDown({
    windowMs,
    delayAfter,
    delayMs: () => delayMs, // Funcție pentru noua versiune
    maxDelayMs: delayMs * 10,
    validate: { delayMs: false }, // Dezactivează avertizarea
  });
};

/**
 * Middleware pentru detectarea și logarea tentativelor de atac
 */
const attackDetection = (req: AuthenticatedRequest, _res: Response, next: NextFunction): void => {
  const suspiciousPatterns = [
    // SQL Injection
    /('|(--)|;|(\|\|)|(\*\*)/i,
    // XSS
    /(<script[^>]*>.*?<\/script>)|(<iframe[^>]*>.*?<\/iframe>)|(<object[^>]*>.*?<\/object>)/i,
    // Path traversal
    /(\.\.[/\\])|(\.\.\.)/,
    // Command injection
    /(;\s*(rm|del|format|shutdown|reboot))/i,
  ];

  const checkForAttacks = (data: unknown, source: string): boolean => {
    if (typeof data === 'string') {
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(data)) {
          logger.logSecurity('potentialAttackDetected', {
            ip: req.ip,
            url: req.originalUrl,
            method: req.method,
            userAgent: req.get('User-Agent'),
            userId: req.user?.id,
            source,
            pattern: pattern.toString(),
            data: data.substring(0, 100), // Limitează datele loggate
          });
          return true;
        }
      }
    } else if (typeof data === 'object' && data !== null) {
      for (const key in data) {
        if (checkForAttacks((data as unknown)[key], `${source}.${key}`)) {
          return true;
        }
      }
    }
    return false;
  };

  // Verifică parametrii URL
  checkForAttacks(req.query, 'query');

  // Verifică body-ul cererii
  checkForAttacks(req.body, 'body');

  // Verifică parametrii rutei
  checkForAttacks(req.params, 'params');

  next();
};

/**
 * Middleware pentru validarea și sanitizarea header-elor
 */
const headerSecurity = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  // Verifică User-Agent suspect
  const userAgent = req.get('User-Agent');
  if (!userAgent || userAgent.length < 10 || userAgent.length > 500) {
    logger.logSecurity('suspiciousUserAgent', {
      ip: req.ip,
      url: req.originalUrl,
      userAgent: userAgent || 'missing',
      userId: req.user?.id,
    });
  }

  // Verifică header-e personalizate suspecte
  const suspiciousHeaders = ['x-forwarded-host', 'x-original-url', 'x-rewrite-url'];
  for (const header of suspiciousHeaders) {
    if (req.get(header)) {
      logger.logSecurity('suspiciousHeaderDetected', {
        ip: req.ip,
        url: req.originalUrl,
        header,
        value: req.get(header),
        userId: req.user?.id,
      });
    }
  }

  // Adaugă header-e de securitate personalizate
  res.set({
    'X-Request-ID': req.id || crypto.randomUUID(),
    'X-Response-Time': Date.now().toString(),
    'X-Content-Type-Options': 'nosniff',
    'X-Download-Options': 'noopen',
    'X-Permitted-Cross-Domain-Policies': 'none',
  });

  next();
};

/**
 * Middleware pentru protecția împotriva clickjacking
 */
const antiClickjacking = (_req: Request, res: Response, next: NextFunction): void => {
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('Content-Security-Policy', "frame-ancestors 'none'");
  next();
};

/**
 * Middleware pentru detectarea bot-urilor
 */
const botDetection = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  const userAgent = req.get('User-Agent') || '';
  const botPatterns = [/bot/i, /crawler/i, /spider/i, /scraper/i, /curl/i, /wget/i, /python-requests/i, /postman/i];

  const isBot = botPatterns.some(pattern => pattern.test(userAgent));

  if (isBot) {
    logger.logSecurity('botDetected', {
      ip: req.ip,
      url: req.originalUrl,
      method: req.method,
      userAgent,
      userId: req.user?.id,
    });

    // Permite doar anumite rute pentru bot-uri
    const allowedBotRoutes = ['/health', '/api/docs', '/robots.txt'];
    if (!allowedBotRoutes.some(route => req.path.startsWith(route))) {
      res.status(403).json({
        success: false,
        error: {
          message: 'Accesul pentru bot-uri nu este permis pe această rută',
        },
      });
      return;
    }
  }

  next();
};

/**
 * Configurare completă de securitate
 */
const setupSecurity = (app: Application): void => {
  // CORS
  app.use(cors(corsOptions));

  // Helmet pentru securitate HTTP
  app.use(helmet(helmetConfig as unknown));

  // Protecție împotriva poluării parametrilor HTTP
  app.use(
    hpp({
      whitelist: ['tags', 'categories'], // Permite array-uri pentru acești parametri
    }),
  );

  // Sanitizare împotriva NoSQL injection
  app.use(mongoSanitize());

  // Protecție XSS
  app.use(xss());

  // Middleware-uri personalizate de securitate
  app.use(headerSecurity);
  app.use(attackDetection);
  app.use(antiClickjacking);
  app.use(botDetection);

  // Rate limiting global
  app.use(
    createRateLimiter(
      15 * 60 * 1000, // 15 minute
      1000, // 1000 cereri
      'Prea multe cereri din această adresă IP. Încercați din nou mai târziu.',
    ),
  );

  // Slow down pentru cereri repetate
  app.use(
    createSlowDown(
      15 * 60 * 1000, // 15 minute
      100, // Începe să încetinească după 100 cereri
      500, // Adaugă 500ms delay
    ),
  );
};

/**
 * Middleware pentru detectarea și blocarea atacurilor SQL injection
 */
const sqlInjectionProtection = (req: Request, res: Response, next: NextFunction) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
    /('|"|;|--|\*|\|)/g,
    /(\b(WAITFOR|DELAY)\b)/gi,
  ];

  const checkValue = (value: unknown): boolean => {
    if (typeof value === 'string') {
      return sqlPatterns.some(pattern => pattern.test(value));
    }
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(checkValue);
    }
    return false;
  };

  const suspicious = checkValue(req.body) || checkValue(req.query) || checkValue(req.params);

  if (suspicious) {
    logger.logSecurity('sqlInjectionAttempt', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      body: req.body,
      query: req.query,
      params: req.params,
      timestamp: new Date().toISOString(),
    });

    return res.status(400).json({
      success: false,
      message: 'Invalid input detected',
      code: 'INVALID_INPUT',
    });
  }

  return next();
};

/**
 * Middleware pentru validarea originii request-urilor
 */
const validateOrigin = (req: Request, _res: Response, next: NextFunction) => {
  const origin = req.get('Origin');
  const referer = req.get('Referer');

  // Logare pentru monitorizare - permite toate originile
  logger.logSecurity('originValidation', {
    ip: req.ip,
    origin: origin || 'no-origin',
    referer: referer || 'no-referer',
    method: req.method,
    path: req.path,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString(),
  });

  // Permite toate originile - ATENȚIE: Configurație permisivă
  // În producție, ar trebui să implementați validări mai stricte
  return next();
};

export {
  setupSecurity,
  corsOptions,
  helmetConfig,
  createRateLimiter,
  createSlowDown,
  attackDetection,
  headerSecurity,
  antiClickjacking,
  botDetection,
  sqlInjectionProtection,
  validateOrigin,
};
