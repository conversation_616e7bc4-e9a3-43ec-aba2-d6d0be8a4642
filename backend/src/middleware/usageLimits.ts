import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../types';
import usageService from '../services/usageService';
// import subscriptionService from '../services/subscriptionService'; // Not used currently
import logger from '../utils/logger';

/**
 * Middleware pentru verificarea limitelor de utilizare
 */
export const checkUsageLimit = (action: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      // Verifică dacă utilizatorul poate efectua acțiunea
      const canPerform = await usageService.canPerformAction(userId.toString(), action);

      if (!canPerform) {
        const stats = await usageService.getUserUsageStats(userId.toString());

        return res.status(403).json({
          success: false,
          message: 'Usage limit exceeded for your current plan',
          data: {
            action,
            currentUsage: stats.currentPeriod,
            limits: stats.limits,
            planType: stats.planType,
            upgradeRequired: true,
          },
        });
      }

      // Dacă poate efectua acțiunea, continuă
      return next();
    } catch {
      logger.error('Error checking usage limits:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  };
};

/**
 * Middleware pentru înregistrarea utilizării după o acțiune reușită
 */
export const trackUsage = (action: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return next();
      }

      // Override res.json pentru a înregistra utilizarea după răspuns
      const originalJson = res.json;
      res.json = function (data: unknown) {
        // Înregistrează utilizarea doar pentru răspunsuri de succes
        if (res.statusCode >= 200 && res.statusCode < 300) {
          usageService.incrementUsage(userId.toString(), action).catch(error => {
            logger.error('Error tracking usage:', error);
          });
        }

        return originalJson.call(this, data);
      };

      next();
    } catch {
      logger.error('Error setting up usage tracking:', error);
      next();
    }
  };
};

/**
 * Middleware combinat pentru verificare și tracking
 */
export const usageMiddleware = (action: string) => {
  return [checkUsageLimit(action), trackUsage(action)];
};

/**
 * Middleware pentru verificarea abonamentului premium
 */
export const requirePremium = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
    }

    const stats = await usageService.getUserUsageStats(userId.toString());

    if (stats.planType !== 'premium') {
      return res.status(403).json({
        success: false,
        message: 'Premium subscription required',
        data: {
          currentPlan: stats.planType,
          requiredPlan: 'premium',
          upgradeRequired: true,
        },
      });
    }

    return next();
  } catch {
    logger.error('Error checking premium subscription:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

/**
 * Middleware pentru verificarea abonamentului basic sau premium
 */
export const requirePaid = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
    }

    const stats = await usageService.getUserUsageStats(userId.toString());

    if (stats.planType === 'free') {
      return res.status(403).json({
        success: false,
        message: 'Paid subscription required',
        data: {
          currentPlan: stats.planType,
          requiredPlans: ['basic', 'premium'],
          upgradeRequired: true,
        },
      });
    }

    return next();
  } catch {
    logger.error('Error checking paid subscription:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

/**
 * Middleware pentru verificarea limitelor specifice
 */
export const checkSpecificLimit = (limitType: 'expenses' | 'categories' | 'exports', customLimit?: number) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const stats = await usageService.getUserUsageStats(userId.toString());

      let currentUsage: number;
      let limit: number;

      switch (limitType) {
        case 'expenses':
          currentUsage = stats.currentPeriod.expenses;
          limit = customLimit || stats.limits.expensesPerMonth;
          break;
        case 'categories':
          currentUsage = stats.currentPeriod.categories;
          limit = customLimit || stats.limits.categories;
          break;
        case 'exports':
          currentUsage = stats.currentPeriod.exports;
          limit = customLimit || stats.limits.exportsPerMonth;
          break;
        default:
          return next();
      }

      if (limit !== -1 && currentUsage >= limit) {
        return res.status(403).json({
          success: false,
          message: `${limitType} limit exceeded`,
          data: {
            limitType,
            currentUsage,
            limit,
            planType: stats.planType,
            upgradeRequired: true,
          },
        });
      }

      next();
    } catch {
      logger.error(`Error checking ${limitType} limit:`, error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  };
};

/**
 * Middleware pentru rate limiting bazat pe plan
 */
export const planBasedRateLimit = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return next();
    }

    const stats = await usageService.getUserUsageStats(userId.toString());

    // Setează header-uri pentru rate limiting bazat pe plan
    switch (stats.planType) {
      case 'free':
        res.set('X-RateLimit-Limit', '100');
        res.set('X-RateLimit-Window', '3600'); // 1 hour
        break;
      case 'basic':
        res.set('X-RateLimit-Limit', '500');
        res.set('X-RateLimit-Window', '3600');
        break;
      case 'premium':
        res.set('X-RateLimit-Limit', '2000');
        res.set('X-RateLimit-Window', '3600');
        break;
    }

    next();
  } catch {
    logger.error('Error setting plan-based rate limits:', error);
    next();
  }
};
