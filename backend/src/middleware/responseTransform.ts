/**
 * Middleware pentru transformarea automată a response-urilor la camelCase
 */

import { Request, Response, NextFunction } from 'express';
import { CaseConverter } from '../utils/caseConverter';
import { safeLog } from '../utils/safeLogger';

interface TransformConfig {
  enabled: boolean;
  logTransformations: boolean;
  skipPaths: string[];
  skipMethods: string[];
  transformArrays: boolean;
  preserveKeys: string[];
}

const defaultConfig: TransformConfig = {
  enabled: true,
  logTransformations: process.env['NODE_ENV'] === 'development',
  skipPaths: ['/api/webhooks', '/api/health'],
  skipMethods: ['OPTIONS'],
  transformArrays: true,
  preserveKeys: ['password', 'token', 'secret', 'key']
};

/**
 * Middleware pentru transformarea automată a response-urilor la camelCase
 */
export const transformResponse = (config: Partial<TransformConfig> = {}) => {
  const finalConfig = { ...defaultConfig, ...config };

  return (req: Request, res: Response, next: NextFunction): void => {
    if (!finalConfig.enabled || shouldSkipTransformation(req, finalConfig)) {
      return next();
    }

    // Interceptează metoda json pentru a transforma response-ul
    const originalJson = res.json;
    
    res.json = function(body: unknown) {
      try {
        if (body && typeof body === 'object') {
          const transformedBody = transformResponseBody(body, finalConfig);
          
          if (finalConfig.logTransformations) {
            safeLog.debug('🔄 Response transformed to camelCase:', {
              action: 'transform_response',
              timestamp: new Date(),
              metadata: {
                path: req.path,
                method: req.method,
                originalKeys: extractKeys(body).slice(0, 10),
                transformedKeys: extractKeys(transformedBody).slice(0, 10)
              }
            });
          }
          
          return originalJson.call(this, transformedBody);
        }
        
        return originalJson.call(this, body);
      } catch {
        safeLog.error('❌ Error transforming response:', error as Error);
        return originalJson.call(this, body);
      }
    };

    next();
  };
};

/**
 * Verifică dacă transformarea trebuie să fie omisă pentru acest request
 */
function shouldSkipTransformation(req: Request, config: TransformConfig): boolean {
  // Skip pentru anumite path-uri
  if (config.skipPaths.some(path => req.path.startsWith(path))) {
    return true;
  }

  // Skip pentru anumite metode HTTP
  if (config.skipMethods.includes(req.method)) {
    return true;
  }

  // Skip pentru request-uri care cer explicit snake_case
  const acceptSnakeCase = req.headers['x-case-format'] === 'snakeCase';
  if (acceptSnakeCase) {
    return true;
  }

  return false;
}

/**
 * Transformă body-ul response-ului la camelCase
 */
function transformResponseBody(body: unknown, _config: TransformConfig): unknown {
  if (!body || typeof body !== 'object') {
    return body;
  }

  // Păstrează structura de bază a response-ului API
  const bodyObj = body as unknown;
  if (bodyObj.success !== undefined || bodyObj.message !== undefined || bodyObj.data !== undefined) {
    return {
      ...body,
      data: bodyObj.data ? CaseConverter.toCamel(bodyObj.data) : bodyObj.data,
      errors: bodyObj.errors ? transformErrors(bodyObj.errors) : bodyObj.errors
    };
  }

  // Pentru alte tipuri de response-uri, transformă complet
  return CaseConverter.toCamel(body);
}

/**
 * Transformă array-ul de erori menținând structura
 */
function transformErrors(errors: unknown[]): unknown[] {
  if (!Array.isArray(errors)) {
    return errors;
  }

  return errors.map(error => {
    if (typeof error === 'object' && error !== null) {
      return CaseConverter.toCamel(error);
    }
    return error;
  });
}

/**
 * Extrage cheile dintr-un obiect pentru logging
 */
function extractKeys(obj: unknown, maxDepth: number = 2, currentDepth: number = 0): string[] {
  if (!obj || typeof obj !== 'object' || currentDepth >= maxDepth) {
    return [];
  }

  const keys: string[] = [];
  
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      keys.push(key);
      
      if (typeof (obj as unknown)[key] === 'object' && (obj as unknown)[key] !== null && !Array.isArray((obj as unknown)[key])) {
        const nestedKeys = extractKeys((obj as unknown)[key], maxDepth, currentDepth + 1);
        keys.push(...nestedKeys.map(nestedKey => `${key}.${nestedKey}`));
      }
    }
  }
  
  return keys;
}

/**
 * Middleware pentru transformarea selectivă bazată pe header-ul clientului
 */
export const adaptiveResponseTransform = () => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const clientFormat = req.headers['x-case-format'] as string;
    const userAgent = req.headers['user-agent'] || '';
    
    // Detectează dacă clientul este frontend-ul nostru (React)
    const isOurFrontend = userAgent.includes('Mozilla') && !userAgent.includes('Postman');
    
    let shouldTransform = false;
    
    if (clientFormat === 'camelCase') {
      shouldTransform = true;
    } else if (clientFormat === 'snakeCase') {
      shouldTransform = false;
    } else if (isOurFrontend) {
      // Default pentru frontend-ul nostru: camelCase
      shouldTransform = true;
    }
    
    if (shouldTransform) {
      return transformResponse({
        logTransformations: process.env['NODE_ENV'] === 'development'
      })(req, res, next);
    }
    
    next();
  };
};

/**
 * Middleware pentru debugging - loghează format-ul request-ului și response-ului
 */
export const debugCaseFormat = () => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    if (process.env['NODE_ENV'] !== 'development') {
      return next();
    }

    const requestFormat = analyzeRequestFormat(req.body);
    const clientFormat = req.headers['x-case-format'];
    
    safeLog.debug('🔍 Case format debug:', {
      action: 'debug_case_format',
      timestamp: new Date(),
      metadata: {
        path: req.path,
        method: req.method,
        clientHeader: clientFormat,
        detectedRequestFormat: requestFormat,
        userAgent: req.headers['user-agent']?.substring(0, 50)
      }
    });

    next();
  };
};

/**
 * Analizează formatul request-ului pentru debugging
 */
function analyzeRequestFormat(body: unknown): 'snake' | 'camel' | 'mixed' | 'unknown' {
  if (!body || typeof body !== 'object') {
    return 'unknown';
  }

  const keys = Object.keys(body);
  if (keys.length === 0) {
    return 'unknown';
  }

  let snakeCount = 0;
  let camelCount = 0;

  keys.forEach(key => {
    if (CaseConverter.isSnakeCase(key)) {
      snakeCount++;
    } else if (CaseConverter.isCamelCase(key)) {
      camelCount++;
    }
  });

  if (snakeCount > camelCount) {
    return 'snake';
  } else if (camelCount > snakeCount) {
    return 'camel';
  } else if (snakeCount > 0 || camelCount > 0) {
    return 'mixed';
  }

  return 'unknown';
}

export default {
  transformResponse,
  adaptiveResponseTransform,
  debugCaseFormat
};
