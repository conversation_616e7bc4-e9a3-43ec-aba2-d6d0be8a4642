{"name": "expense-tracker-backend", "version": "1.0.0", "description": "Backend API pentru aplicația de tracking cheltuieli", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "nodemon", "dev:build": "tsc --watch", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:integration": "jest --testPathPattern=integration", "lint": "eslint src/ --ext .ts", "lint:fix": "eslint src/ --ext .ts --fix", "format": "prettier --write src/", "clean": "if exist dist rmdir /s /q dist", "prebuild": "npm run clean", "prestart": "npm run build", "db:generate": "npx prisma generate", "db:migrate": "npx prisma migrate dev", "db:deploy": "npx prisma migrate deploy", "db:seed": "npx prisma db seed", "db:reset": "npx prisma migrate reset", "db:studio": "npx prisma studio", "db:push": "npx prisma db push", "migrate:cuid:generate": "node scripts/generateCuids.js generate", "migrate:cuid:apply": "node scripts/generateCuids.js apply", "migrate:cuid:full": "node scripts/generateCuids.js full", "stripe:plans": "node scripts/initializePlans.js", "stripe:webhooks": "node scripts/setupStripeWebhooks.js", "stripe:webhooks:list": "node scripts/setupStripeWebhooks.js list", "stripe:webhooks:setup": "node scripts/setupStripeWebhooks.js setup", "setup:monetization": "npm run db:migrate && npm run stripe:plans", "stripe:listen": "stripe listen --forward-to localhost:3000/api/webhooks/stripe", "security:audit": "npm audit", "security:fix": "npm audit fix", "security:check": "npm audit --audit-level moderate", "prepare": "husky install", "pre-commit": "lint-staged"}, "keywords": ["expense-tracker", "api", "nodejs", "express", "mvp"], "author": "Your Name", "license": "MIT", "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^5.22.0", "@types/pdfkit": "^0.17.0", "@types/redis": "^4.0.10", "@types/sequelize": "^4.28.20", "@types/uuid": "^10.0.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "helmet": "^7.1.0", "hpp": "^0.2.3", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pdfkit": "^0.15.2", "pg": "^8.11.3", "redis": "^5.6.0", "sequelize": "^6.37.7", "stripe": "^14.25.0", "uuid": "^11.1.0", "winston": "^3.17.0", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/hpp": "^0.2.6", "@types/ioredis": "^4.28.10", "@types/jest": "^30.0.0", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^20.19.4", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitest/ui": "^3.2.4", "eslint": "^8.55.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-security": "^3.0.1", "husky": "^9.1.7", "ioredis": "^5.6.1", "jest": "^29.7.0", "lint-staged": "^16.1.2", "nodemon": "^3.1.10", "prettier": "^3.1.0", "prisma": "^5.7.0", "supertest": "^6.3.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "prisma": {"seed": "node prisma/seed.js"}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}