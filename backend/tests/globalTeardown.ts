const { unlink } = require('fs/promises');
const { join } = require('path');

module.exports = async function globalTeardown() {
  console.log('🧹 Cleaning up test environment...');
  
  try {
    // Șterge baza de date de test
    const testDbPath = join(__dirname, '../test.db');
    try {
      await unlink(testDbPath);
      console.log('🗑️ Test database removed');
    } catch {
      // Fișierul poate să nu existe, nu e o problemă
      if ((error as unknown).code !== 'ENOENT') {
        console.warn('⚠️ Could not remove test database:', error);
      }
    }
    
    // Șterge fișierele temporare de test
    const tempFiles = [
      join(__dirname, '../test.db-journal'),
      join(__dirname, '../test.db-wal'),
      join(__dirname, '../test.db-shm')
    ];
    
    for (const file of tempFiles) {
      try {
        await unlink(file);
      } catch {
        // Ignoră erorile pentru fișierele care nu există
        if ((error as unknown).code !== 'ENOENT') {
          console.warn(`⚠️ Could not remove ${file}:`, error);
        }
      }
    }
    
    console.log('✅ Test environment cleanup complete');
  } catch {
    console.error('❌ Failed to cleanup test environment:', error);
    // Nu aruncăm eroarea pentru că cleanup-ul nu ar trebui să oprească procesul
  }
}