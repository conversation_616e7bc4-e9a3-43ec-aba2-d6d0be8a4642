const { prisma: testPrisma, _createTestUser, _createTestCategory, _createTestExpense } = require('../../setup');

// Mock the usageService to avoid database calls
const mockUsageService = {
  getUserUsageStats: jest.fn(),
  canPerformAction: jest.fn(),
  incrementUsage: jest.fn(),
  getUsageProgress: jest.fn(),
  resetUsageForPeriod: jest.fn(),
  getGlobalUsageStats: jest.fn(),
};

jest.mock('../../../src/services/usageService', () => mockUsageService);
const usageService = require('../../../src/services/usageService');

// Mock prisma calls
jest.mock('../../setup', () => ({
  prisma: {
    usageLog: {
      findFirst: jest.fn(),
      count: jest.fn(),
    },
  },
  createTestUser: jest.fn(),
  createTestCategory: jest.fn(),
  createTestExpense: jest.fn(),
}));

const mockPrisma = testPrisma as unknown;
const mockCreateTestUser = createTestUser as unknown;
const mockCreateTestCategory = createTestCategory as unknown;
const mockCreateTestExpense = createTestExpense as unknown;

describe('UsageService', () => {
  let testUser: unknown;
  let testCategory: unknown;

  beforeEach(async () => {
    // Setup mock data
    testUser = { id: 1, email: '<EMAIL>', plan_type: 'free' };
    testCategory = { id: 1, name: 'Test Category', userId: 1 };
    
    // Setup mock implementations
    mockCreateTestUser.mockResolvedValue(testUser);
    mockCreateTestCategory.mockResolvedValue(testCategory);
    mockCreateTestExpense.mockResolvedValue({ id: 1, amount: 10.50, description: 'Test expense' });
    
    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('getUserUsageStats', () => {
    it('should return correct usage stats for free plan user', async () => {
      // Arrange
      const userId = testUser.id.toString();
      const expectedStats = {
        current_period: {
          expenses: 0,
          categories: 1,
          exports: 0,
        },
        limits: {
          expenses_per_month: 50,
          categories: 5,
          exports_per_month: 3,
        },
        usage_percentage: {
          expenses: 0,
          categories: 20,
          exports: 0,
        },
        plan_type: 'free',
      };
      
      mockUsageService.getUserUsageStats.mockResolvedValue(expectedStats);

      // Act
      const stats = await usageService.getUserUsageStats(userId);

      // Assert
      expect(mockUsageService.getUserUsageStats).toHaveBeenCalledWith(userId);
      expect(stats).toMatchObject(expectedStats);
    });

    it('should return correct usage stats for premium plan user', async () => {
      // Arrange
      const premiumUser = { id: 2, email: '<EMAIL>', plan_type: 'premium' };
      const userId = premiumUser.id.toString();
      const expectedStats = {
        limits: {
          expenses_per_month: -1,
          categories: -1,
          exports_per_month: -1,
        },
        usage_percentage: {
          expenses: 0,
          categories: 0,
          exports: 0,
        },
        plan_type: 'premium',
      };
      
      mockUsageService.getUserUsageStats.mockResolvedValue(expectedStats);

      // Act
      const stats = await usageService.getUserUsageStats(userId);

      // Assert
      expect(mockUsageService.getUserUsageStats).toHaveBeenCalledWith(userId);
      expect(stats).toMatchObject(expectedStats);
    });

    it('should calculate correct expense count for current month', async () => {
      // Arrange
      const userId = testUser.id.toString();
      const expectedStats = {
        current_period: {
          expenses: 2,
          categories: 1,
          exports: 0,
        },
        limits: {
          expenses_per_month: 50,
          categories: 5,
          exports_per_month: 3,
        },
        usage_percentage: {
          expenses: 4,
          categories: 20,
          exports: 0,
        },
        plan_type: 'free',
      };
      
      mockUsageService.getUserUsageStats.mockResolvedValue(expectedStats);

      // Act
      const stats = await usageService.getUserUsageStats(userId);

      // Assert
      expect(mockUsageService.getUserUsageStats).toHaveBeenCalledWith(userId);
      expect(stats.current_period.expenses).toBe(2);
      expect(stats.usage_percentage.expenses).toBe(4);
    });

    it('should throw error for non-existent user', async () => {
      // Arrange
      const nonExistentUserId = '99999';
      mockUsageService.getUserUsageStats.mockRejectedValue(new Error('User not found'));

      // Act & Assert
      await expect(usageService.getUserUsageStats(nonExistentUserId))
        .rejects.toThrow('User not found');
      expect(mockUsageService.getUserUsageStats).toHaveBeenCalledWith(nonExistentUserId);
    });
  });

  describe('canPerformAction', () => {
    it('should allow creating expense when under limit', async () => {
      // Arrange
      const userId = testUser.id.toString();
      mockUsageService.canPerformAction.mockResolvedValue(true);

      // Act
      const canCreate = await usageService.canPerformAction(userId, 'create_expense');

      // Assert
      expect(mockUsageService.canPerformAction).toHaveBeenCalledWith(userId, 'create_expense');
      expect(canCreate).toBe(true);
    });

    it('should deny creating expense when at limit', async () => {
      // Arrange
      const userId = testUser.id.toString();
      mockUsageService.canPerformAction.mockResolvedValue(false);

      // Act
      const canCreate = await usageService.canPerformAction(userId, 'create_expense');

      // Assert
      expect(mockUsageService.canPerformAction).toHaveBeenCalledWith(userId, 'create_expense');
      expect(canCreate).toBe(false);
    });

    it('should allow unlimited actions for premium users', async () => {
      // Arrange
      const premiumUser = { id: 2, email: '<EMAIL>', plan_type: 'premium' };
      const userId = premiumUser.id.toString();
      mockUsageService.canPerformAction.mockResolvedValue(true);

      // Act
      const canCreateExpense = await usageService.canPerformAction(userId, 'create_expense');
      const canCreateCategory = await usageService.canPerformAction(userId, 'create_category');
      const canExport = await usageService.canPerformAction(userId, 'export_data');

      // Assert
      expect(mockUsageService.canPerformAction).toHaveBeenCalledWith(userId, 'create_expense');
      expect(mockUsageService.canPerformAction).toHaveBeenCalledWith(userId, 'create_category');
      expect(mockUsageService.canPerformAction).toHaveBeenCalledWith(userId, 'export_data');
      expect(canCreateExpense).toBe(true);
      expect(canCreateCategory).toBe(true);
      expect(canExport).toBe(true);
    });

    it('should deny creating category when at limit', async () => {
      // Arrange
      const userId = testUser.id.toString();
      mockUsageService.canPerformAction.mockResolvedValue(false);

      // Act
      const canCreate = await usageService.canPerformAction(userId, 'create_category');

      // Assert
      expect(mockUsageService.canPerformAction).toHaveBeenCalledWith(userId, 'create_category');
      expect(canCreate).toBe(false);
    });

    it('should allow unknown actions by default', async () => {
      // Arrange
      const userId = testUser.id.toString();
      mockUsageService.canPerformAction.mockResolvedValue(true);

      // Act
      const canPerform = await usageService.canPerformAction(userId, 'unknown_action');

      // Assert
      expect(mockUsageService.canPerformAction).toHaveBeenCalledWith(userId, 'unknown_action');
      expect(canPerform).toBe(true);
    });
  });

  describe('incrementUsage', () => {
    it('should create usage log entry', async () => {
      // Arrange
      const userId = testUser.id.toString();
      const action = 'create_expense';
      const mockUsageLog = { id: 1, userId: testUser.id, action, resource: 'expense', date: new Date() };
      
      mockUsageService.incrementUsage.mockResolvedValue(undefined);
      mockPrisma.usageLog.findFirst.mockResolvedValue(mockUsageLog);

      // Act
      await usageService.incrementUsage(userId, action);

      // Assert
      expect(mockUsageService.incrementUsage).toHaveBeenCalledWith(userId, action);
      
      // Simulate checking the created log
      const usageLog = await testPrisma.usageLog.findFirst({
        where: {
          userId: testUser.id,
          action,
        },
      });

      expect(usageLog).toBeTruthy();
      expect(usageLog?.resource).toBe('expense');
    });

    it('should handle different action types correctly', async () => {
      // Arrange
      const userId = testUser.id.toString();
      const actions = [
        { action: 'create_category', expectedResource: 'category' },
        { action: 'export_data', expectedResource: 'export' },
        { action: 'unknown_action', expectedResource: 'unknown' },
      ];

      mockUsageService.incrementUsage.mockResolvedValue(undefined);

      // Act & Assert
      for (const { action, expectedResource } of actions) {
        const mockUsageLog = { id: 1, userId: testUser.id, action, resource: expectedResource, date: new Date() };
        mockPrisma.usageLog.findFirst.mockResolvedValue(mockUsageLog);
        
        await usageService.incrementUsage(userId, action);
        
        expect(mockUsageService.incrementUsage).toHaveBeenCalledWith(userId, action);
        
        // Simulate checking the created log
        const usageLog = await testPrisma.usageLog.findFirst({
          where: {
            userId: testUser.id,
            action,
          },
          orderBy: { date: 'desc' },
        });

        expect(usageLog).toBeTruthy();
        expect(usageLog?.resource).toBe(expectedResource);
      }
    });
  });

  describe('getUsageProgress', () => {
    it('should return correct progress information', async () => {
      // Arrange
      const userId = testUser.id.toString();
      const expectedProgress = {
        expenses: {
          current: 1,
          limit: 50,
          percentage: 2,
          unlimited: false,
        },
        categories: {
          current: 1,
          limit: 5,
          percentage: 20,
          unlimited: false,
        },
        exports: {
          current: 1,
          limit: 3,
          percentage: 33,
          unlimited: false,
        },
        plan_type: 'free',
      };
      
      mockUsageService.getUsageProgress.mockResolvedValue(expectedProgress);

      // Act
      const progress = await usageService.getUsageProgress(userId);

      // Assert
      expect(mockUsageService.getUsageProgress).toHaveBeenCalledWith(userId);
      expect(progress).toMatchObject(expectedProgress);
    });

    it('should show unlimited features for premium users', async () => {
      // Arrange
      const premiumUser = await createTestUser({
        plan_type: 'premium',
      });
      const userId = premiumUser.id.toString();
      const expectedProgress = {
        expenses: {
          current: 0,
          limit: -1,
          percentage: 0,
          unlimited: true,
        },
        categories: {
          current: 0,
          limit: -1,
          percentage: 0,
          unlimited: true,
        },
        exports: {
          current: 0,
          limit: -1,
          percentage: 0,
          unlimited: true,
        },
        plan_type: 'premium',
      };
      
      mockUsageService.getUsageProgress.mockResolvedValue(expectedProgress);

      // Act
      const progress = await usageService.getUsageProgress(userId);

      // Assert
      expect(mockUsageService.getUsageProgress).toHaveBeenCalledWith(userId);
      expect(progress.expenses.unlimited).toBe(true);
      expect(progress.categories.unlimited).toBe(true);
      expect(progress.exports.unlimited).toBe(true);
    });
  });

  describe('resetUsageForPeriod', () => {
    it('should reset usage logs for specified period', async () => {
      // Arrange
      const userId = testUser.id.toString();
      
      mockUsageService.resetUsageForPeriod.mockResolvedValue(undefined);
      mockPrisma.usageLog.count.mockResolvedValueOnce(2).mockResolvedValueOnce(0);
      
      // Verify logs exist
      const logsBefore = await testPrisma.usageLog.count({
        where: { userId: testUser.id },
      });
      expect(logsBefore).toBeGreaterThan(0);

      // Act
      await usageService.resetUsageForPeriod(userId, 'monthly');

      // Assert
      expect(mockUsageService.resetUsageForPeriod).toHaveBeenCalledWith(userId, 'monthly');
      
      // Verify logs were deleted
      const logsAfter = await testPrisma.usageLog.count({
        where: { userId: testUser.id },
      });
      expect(logsAfter).toBe(0);
    });
  });

  describe('getGlobalUsageStats', () => {
    it('should return global statistics', async () => {
      // Arrange
      const expectedStats = {
        total_users: 2,
        active_users: 1,
        total_expenses: 0,
        total_categories: 1,
        total_exports: 0,
        period: 'monthly'
      };
      
      mockUsageService.getGlobalUsageStats.mockResolvedValue(expectedStats);

      // Act
      const stats = await usageService.getGlobalUsageStats();

      // Assert
      expect(mockUsageService.getGlobalUsageStats).toHaveBeenCalled();
      expect(stats).toMatchObject({
        total_users: 2,
        active_users: 1, // only user2 has recent login
        total_expenses: 0,
        total_categories: 1, // testCategory
        total_exports: 0,
      });
      expect(stats.period).toBeDefined();
    });
  });
});
