# 🧪 Backend Tests Organization

## 📁 Directory Structure

```
tests/
├── README.md                 # This documentation
├── setup.ts                  # Test setup and utilities
├── globalSetup.ts            # Global test environment setup
├── globalTeardown.ts         # Global test environment cleanup
├── unit/                     # Unit tests
│   ├── controllers/          # Controller unit tests
│   ├── services/             # Service unit tests
│   │   └── usageService.test.ts
│   └── middleware/           # Middleware unit tests
└── integration/              # Integration tests
    └── expenses.test.ts      # Expenses API integration tests
```

## 🎯 Test Types

### Unit Tests (`/unit`)
- **Controllers**: Test individual controller methods in isolation
- **Services**: Test business logic and service methods
- **Middleware**: Test middleware functions and their behavior

### Integration Tests (`/integration`)
- Test complete API endpoints
- Test database interactions
- Test service integrations

## 🔧 Configuration

- **Jest Config**: `jest.config.ts` in project root
- **Test Environment**: Node.js with ts-jest
- **Database**: SQLite in-memory for tests
- **Setup**: Automatic database reset before each test suite

## 🚀 Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- usageService.test.ts

# Run tests in specific directory
npm test -- tests/unit/services
```

## 📝 Test Naming Conventions

- **Files**: `*.test.ts` or `*.spec.ts`
- **Describe blocks**: Use the class/function name being tested
- **Test cases**: Use descriptive "should" statements

### Example:
```typescript
describe('UserService', () => {
  describe('createUser', () => {
    it('should create a new user with valid data', () => {
      // Test implementation
    });
    
    it('should throw error when email already exists', () => {
      // Test implementation
    });
  });
});
```

## 🛠️ Test Utilities

- `createTestUser()`: Creates a test user in database
- `createTestCategory()`: Creates a test category
- `createTestExpense()`: Creates a test expense
- `prisma`: Test database client

## 📊 Coverage Requirements

- **Global**: 85% (branches, functions, lines, statements)
- **Services**: 90% (higher requirement for business logic)
- **Controllers**: 80% (focused on request/response handling)

## 🔒 Security Testing

- Authentication tests
- Authorization tests
- Input validation tests
- Rate limiting tests