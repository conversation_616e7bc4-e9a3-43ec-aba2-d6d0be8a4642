module.exports = {
  parser: '@typescript-eslint/parser',
  env: {
    node: true,
    es2023: true,
    jest: true
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended'
  ],
  plugins: [
    '@typescript-eslint'
  ],
  parserOptions: {
    ecmaVersion: 2023,
    sourceType: 'module',
    project: './tsconfig.json',
    tsconfigRootDir: __dirname
  },
  ignorePatterns: [
    'dist/',
    'node_modules/',
    '*.js',
    '*.d.ts',
    'jest.config.ts',
    'vitest.config.ts'
  ],
  rules: {
    // TypeScript specific
    '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-explicit-any': 'warn', // Changed from error to warn
    '@typescript-eslint/no-empty-object-type': 'warn', // Replaced ban-types
    '@typescript-eslint/no-unsafe-assignment': 'warn', // Changed from error to warn
    '@typescript-eslint/no-unsafe-call': 'warn', // Changed from error to warn
    '@typescript-eslint/no-unsafe-member-access': 'warn', // Changed from error to warn
    '@typescript-eslint/no-unsafe-return': 'warn', // Changed from error to warn
    '@typescript-eslint/no-unsafe-function-type': 'warn', // Added for Function type
    '@typescript-eslint/no-require-imports': 'warn', // Added for require() imports
    
    // Security rules
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-script-url': 'error',
    'no-caller': 'error',
    'no-extend-native': 'error',
    'no-extra-bind': 'error',
    'no-iterator': 'error',
    'no-lone-blocks': 'error',
    'no-loop-func': 'error',
    'no-multi-str': 'error',
    'no-new-wrappers': 'error',
    'no-octal-escape': 'error',
    'no-proto': 'error',
    'no-return-assign': 'error',
    'no-self-compare': 'error',
    'no-sequences': 'error',
    'no-throw-literal': 'error',
    'no-unused-expressions': 'error',
    'no-useless-call': 'error',
    'no-useless-concat': 'error',
    'no-void': 'error',
    'no-with': 'error',
    
    // Production safety
    'no-console': process.env['NODE_ENV'] === 'production' ? 'error' : 'warn',
    'no-debugger': process.env['NODE_ENV'] === 'production' ? 'error' : 'warn',
    'no-alert': 'error',
    
    // General rules
    'prefer-const': 'error',
    'no-var': 'error',
    'object-shorthand': 'error',
    'prefer-arrow-callback': 'error',
    'prefer-template': 'error',
    'prefer-spread': 'error',
    'prefer-rest-params': 'error'
  }
};