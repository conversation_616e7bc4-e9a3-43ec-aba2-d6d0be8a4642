import { PrismaClient } from '@prisma/client';
// import Stripe from 'stripe'; // Not used currently
import { config } from 'dotenv';

config();

// const stripe = new Stripe(process.env['STRIPE_SECRET_KEY'] || '', {
//   apiVersion: '2023-10-16',
// });

const prisma = new PrismaClient();

const plans = [
  {
    stripeId: 'free_plan',
    name: 'Free',
    price: 0,
    currency: 'USD',
    interval: 'month',
    features: {
      expense_limit: parseInt(process.env.FREE_PLAN_EXPENSE_LIMIT) || 50,
      data_export: false,
      advanced_reports: false,
      priority_support: false,
      api_access: false
    },
    limits: {
      monthly_expenses: parseInt(process.env.FREE_PLAN_EXPENSE_LIMIT) || 50
    },
    description: 'Perfect for getting started with expense tracking',
    isActive: true,
    sortOrder: 1
  },
  {
    stripeId: process.env.STRIPE_BASIC_PLAN_ID || 'basic_plan',
    name: 'Basic',
    price: 9.99,
    currency: 'USD',
    interval: 'month',
    features: {
      expense_limit: parseInt(process.env.BASIC_PLAN_EXPENSE_LIMIT) || 500,
      data_export: true,
      advanced_reports: false,
      priority_support: false,
      api_access: false
    },
    limits: {
      monthly_expenses: parseInt(process.env.BASIC_PLAN_EXPENSE_LIMIT) || 500
    },
    description: 'Great for individuals and small businesses',
    isActive: true,
    sortOrder: 2
  },
  {
    stripeId: process.env.STRIPE_PREMIUM_PLAN_ID || 'premium_plan',
    name: 'Premium',
    price: 19.99,
    currency: 'USD',
    interval: 'month',
    features: {
      expense_limit: -1, // unlimited
      data_export: true,
      advanced_reports: true,
      priority_support: true,
      api_access: true
    },
    limits: {
      monthly_expenses: -1 // unlimited
    },
    description: 'Perfect for power users and businesses',
    isActive: true,
    sortOrder: 3
  }
];

async function initializePlans() {
  try {
    console.log('🚀 Initializing subscription plans...');
    
    for (const planData of plans) {
      // Check if plan already exists
      const existingPlan = await prisma.plan.findFirst({
        where: { stripeId: planData.stripeId }
      });
      
      if (existingPlan) {
        console.log(`✅ Plan '${planData.name}' already exists, updating...`);
        await prisma.plan.update({
          where: { id: existingPlan.id },
          data: planData
        });
      } else {
        console.log(`📦 Creating plan '${planData.name}'...`);
        await prisma.plan.create({
          data: planData
        });
      }
    }
    
    console.log('✅ All plans initialized successfully!');
    
    // Display created plans
    const allPlans = await prisma.plan.findMany({
      orderBy: { price: 'asc' }
    });
    
    console.log('\n📋 Available plans:');
    allPlans.forEach(plan => {
      const price = Number(plan.price) === 0 ? 'Free' : `$${Number(plan.price).toFixed(2)}/${plan.interval}`;
      console.log(`  - ${plan.name} (${plan.stripeId}): ${price}`);
      const features = plan.features as unknown;
      console.log(`    Expense limit: ${features?.expense_limit === -1 ? 'Unlimited' : features?.expense_limit || 'N/A'}`);
      console.log(`    Features: ${features ? Object.entries(features).filter(([, value]) => value === true).map(([key]) => key).join(', ') || 'Basic features' : 'N/A'}`);
      console.log('');
    });
    
  } catch {
    console.error('❌ Error initializing plans:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  initializePlans()
    .then(() => {
      console.log('🎉 Plan initialization completed!');
      process.exit(0);
    })
    .catch((_error) => {
      console.error('💥 Plan initialization failed:', error);
      process.exit(1);
    });
}

module.exports = { initializePlans };