const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyCuidMigration() {
  console.log('🔍 Verificarea migrării CUID-urilor...\n');

  try {
    // Verifică utilizatorii
    const users = await prisma.user.findMany({
      select: { id: true, email: true, firstName: true, lastName: true }
    });
    console.log(`👥 Utilizatori: ${users.length}`);
    if (users.length > 0) {
      console.log(`   Exemplu ID: ${users[0].id} (lungime: ${users[0].id.length})`);
      console.log(`   Format CUID: ${users[0].id.startsWith('c') ? '✅' : '❌'}`);
    }

    // Verifică categoriile
    const categories = await prisma.category.findMany({
      select: { id: true, name: true, userId: true }
    });
    console.log(`\n📂 Categorii: ${categories.length}`);
    if (categories.length > 0) {
      console.log(`   Exemplu ID: ${categories[0].id} (lungime: ${categories[0].id.length})`);
      console.log(`   Format CUID: ${categories[0].id.startsWith('c') ? '✅' : '❌'}`);
      console.log(`   User ID: ${categories[0].userId} (lungime: ${categories[0].userId.length})`);
      console.log(`   User ID CUID: ${categories[0].userId.startsWith('c') ? '✅' : '❌'}`);
    }

    // Verifică cheltuielile
    const expenses = await prisma.expense.findMany({
      select: { id: true, description: true, userId: true, categoryId: true }
    });
    console.log(`\n💰 Cheltuieli: ${expenses.length}`);
    if (expenses.length > 0) {
      console.log(`   Exemplu ID: ${expenses[0].id} (lungime: ${expenses[0].id.length})`);
      console.log(`   Format CUID: ${expenses[0].id.startsWith('c') ? '✅' : '❌'}`);
      console.log(`   User ID: ${expenses[0].userId} (lungime: ${expenses[0].userId.length})`);
      console.log(`   User ID CUID: ${expenses[0].userId.startsWith('c') ? '✅' : '❌'}`);
      console.log(`   Category ID: ${expenses[0].categoryId} (lungime: ${expenses[0].categoryId.length})`);
      console.log(`   Category ID CUID: ${expenses[0].categoryId.startsWith('c') ? '✅' : '❌'}`);
    }

    // Verifică planurile
    const plans = await prisma.plan.findMany({
      select: { id: true, name: true }
    });
    console.log(`\n📋 Planuri: ${plans.length}`);
    if (plans.length > 0) {
      console.log(`   Exemplu ID: ${plans[0].id} (lungime: ${plans[0].id.length})`);
      console.log(`   Format CUID: ${plans[0].id.startsWith('c') ? '✅' : '❌'}`);
    }

    // Verifică abonamentele
    const subscriptions = await prisma.subscription.findMany({
      select: { id: true, userId: true, planId: true }
    });
    console.log(`\n🔔 Abonamente: ${subscriptions.length}`);
    if (subscriptions.length > 0) {
      console.log(`   Exemplu ID: ${subscriptions[0].id} (lungime: ${subscriptions[0].id.length})`);
      console.log(`   Format CUID: ${subscriptions[0].id.startsWith('c') ? '✅' : '❌'}`);
      console.log(`   User ID: ${subscriptions[0].userId} (lungime: ${subscriptions[0].userId.length})`);
      console.log(`   User ID CUID: ${subscriptions[0].userId.startsWith('c') ? '✅' : '❌'}`);
      console.log(`   Plan ID: ${subscriptions[0].planId} (lungime: ${subscriptions[0].planId.length})`);
      console.log(`   Plan ID CUID: ${subscriptions[0].planId.startsWith('c') ? '✅' : '❌'}`);
    }

    // Verifică usage logs
    const usageLogs = await prisma.usageLog.findMany({
      select: { id: true, userId: true, resourceId: true }
    });
    console.log(`\n📊 Usage Logs: ${usageLogs.length}`);
    if (usageLogs.length > 0) {
      console.log(`   Exemplu ID: ${usageLogs[0].id} (lungime: ${usageLogs[0].id.length})`);
      console.log(`   Format CUID: ${usageLogs[0].id.startsWith('c') ? '✅' : '❌'}`);
      console.log(`   User ID: ${usageLogs[0].userId} (lungime: ${usageLogs[0].userId.length})`);
      console.log(`   User ID CUID: ${usageLogs[0].userId.startsWith('c') ? '✅' : '❌'}`);
      if (usageLogs[0].resourceId) {
        console.log(`   Resource ID: ${usageLogs[0].resourceId} (lungime: ${usageLogs[0].resourceId.length})`);
        console.log(`   Resource ID CUID: ${usageLogs[0].resourceId.startsWith('c') ? '✅' : '❌'}`);
      }
    }

    // Verifică webhook events
    const webhookEvents = await prisma.webhookEvent.findMany({
      select: { id: true, type: true }
    });
    console.log(`\n🔗 Webhook Events: ${webhookEvents.length}`);
    if (webhookEvents.length > 0) {
      console.log(`   Exemplu ID: ${webhookEvents[0].id} (lungime: ${webhookEvents[0].id.length})`);
      console.log(`   Format CUID: ${webhookEvents[0].id.startsWith('c') ? '✅' : '❌'}`);
    }

    console.log('\n✅ Verificarea completă!');

  } catch {
    console.error('❌ Eroare la verificarea migrării:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyCuidMigration();
