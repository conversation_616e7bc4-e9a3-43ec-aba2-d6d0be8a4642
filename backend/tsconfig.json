{"compilerOptions": {"target": "ES2023", "module": "commonjs", "moduleResolution": "node", "lib": ["ES2020", "DOM"], "outDir": "./dist", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/middleware/*": ["middleware/*"], "@/routes/*": ["routes/*"], "@/models/*": ["models/*"], "@/services/*": ["services/*"], "@/controllers/*": ["controllers/*"]}, "strict": true, "verbatimModuleSyntax": false, "incremental": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "exactOptionalPropertyTypes": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitReturns": true, "noImplicitThis": true, "noPropertyAccessFromIndexSignature": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "noUnusedLocals": true, "noUnusedParameters": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "tsBuildInfoFile": "./dist/.tsbuildinfo", "types": ["node", "jest"]}, "include": ["src/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"transpileOnly": true}}