/**
 * <PERSON>ript pentru rezolvarea problemelor de securitate identificate de ESLint în frontend
 */

import { promises as fs } from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Tipuri pentru configurare
interface SecurityFix {
  pattern: RegExp;
  replacement: string;
  description: string;
  fileTypes: string[];
}

interface FixResult {
  file: string;
  fixes: number;
  errors: string[];
}

// Configurări pentru fix-uri automate
const securityFixes: SecurityFix[] = [
  {
    pattern: /console\.log\(/g,
    replacement: 'safeLog.debug(',
    description: 'Replace console.log with safe logging',
    fileTypes: ['.ts', '.tsx', '.js', '.jsx']
  },
  {
    pattern: /console\.error\(/g,
    replacement: 'safeLog.error(',
    description: 'Replace console.error with safe logging',
    fileTypes: ['.ts', '.tsx', '.js', '.jsx']
  },
  {
    pattern: /console\.warn\(/g,
    replacement: 'safeLog.warn(',
    description: 'Replace console.warn with safe logging',
    fileTypes: ['.ts', '.tsx', '.js', '.jsx']
  },
  {
    pattern: /console\.info\(/g,
    replacement: 'safeLog.info(',
    description: 'Replace console.info with safe logging',
    fileTypes: ['.ts', '.tsx', '.js', '.jsx']
  },
  {
    pattern: /: unknown\b/g,
    replacement: ': unknown',
    description: 'Replace explicit any with unknown',
    fileTypes: ['.ts', '.tsx']
  },
  {
    pattern: /\bany\[\]/g,
    replacement: 'unknown[]',
    description: 'Replace unknown[] with unknown[]',
    fileTypes: ['.ts', '.tsx']
  },
  {
    pattern: /Array<unknown>/g,
    replacement: 'Array<unknown>',
    description: 'Replace Array<unknown> with Array<unknown>',
    fileTypes: ['.ts', '.tsx']
  },
  {
    pattern: /React\.FC<any>/g,
    replacement: 'React.FC<Record<string, unknown>>',
    description: 'Replace React.FC<Record<string, unknown>> with safe props type',
    fileTypes: ['.tsx']
  },
  {
    pattern: /useState<unknown>/g,
    replacement: 'useState<unknown>',
    description: 'Replace useState<unknown> with unknown',
    fileTypes: ['.tsx', '.ts']
  }
];

// Funcții utilitare
class SecurityFixer {
  private srcDir: string;
  private results: FixResult[] = [];

  constructor(srcDir: string) {
    this.srcDir = srcDir;
  }

  /**
   * Găsește toate fișierele TypeScript/JavaScript/React
   */
  private async findFiles(dir: string, extensions: string[]): Promise<string[]> {
    const files: string[] = [];
    
    const entries = await fs.readdir(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
        const subFiles = await this.findFiles(fullPath, extensions);
        files.push(...subFiles);
      } else if (entry.isFile() && extensions.some(ext => entry.name.endsWith(ext))) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  /**
   * Determină calea relativă pentru import în funcție de locația fișierului
   */
  private getImportPath(filePath: string): string {
    const relativePath = path.relative(path.dirname(filePath), path.join(this.srcDir, 'utils', 'safeLogger'));
    return relativePath.replace(/\\/g, '/').replace(/\.ts$/, '');
  }

  /**
   * Aplică fix-urile pe un fișier
   */
  private async fixFile(filePath: string): Promise<FixResult> {
    const result: FixResult = {
      file: filePath,
      fixes: 0,
      errors: []
    };

    try {
      let content = await fs.readFile(filePath, 'utf8');
      const originalContent = content;
      
      // Verifică dacă fișierul are deja import pentru safeLog
      const hasSafeLogImport = content.includes('safeLog') || content.includes('from \'../utils/safeLogger\'') || content.includes('from \'./utils/safeLogger\'');
      let needsSafeLogImport = false;
      
      // Aplică fix-urile
      for (const fix of securityFixes) {
        if (fix.fileTypes.some(ext => filePath.endsWith(ext))) {
          const matches = content.match(fix.pattern);
          if (matches) {
            content = content.replace(fix.pattern, fix.replacement);
            result.fixes += matches.length;
            
            // Marchează că avem nevoie de import pentru safeLog
            if (fix.replacement.includes('safeLog')) {
              needsSafeLogImport = true;
            }
          }
        }
      }
      
      // Adaugă import pentru safeLog dacă este necesar
      if (needsSafeLogImport && !hasSafeLogImport) {
        const importPath = this.getImportPath(filePath);
        const importStatement = `import { safeLog } from '${importPath}';\n`;
        
        // Găsește locul potrivit pentru import
        const importRegex = /^import.*from.*['"];?$/gm;
        const imports = content.match(importRegex);
        
        if (imports && imports.length > 0) {
          // Adaugă după ultimul import
          const lastImport = imports[imports.length - 1];
          if (lastImport) {
            const lastImportIndex = content.lastIndexOf(lastImport);
            const insertIndex = lastImportIndex + lastImport.length + 1;
            content = content.slice(0, insertIndex) + importStatement + content.slice(insertIndex);
          } else {
            // Fallback: adaugă la început
            content = importStatement + '\n' + content;
          }
        } else {
          // Adaugă la începutul fișierului, după eventualele comentarii
          const lines = content.split('\n');
          let insertIndex = 0;
          
          // Sari peste comentariile de la început
          while (insertIndex < lines.length) {
            const line = lines[insertIndex];
            if (!line || 
                (!line.trim().startsWith('//') && 
                 !line.trim().startsWith('/*') && 
                 !line.trim().startsWith('*') &&
                 line.trim() !== '')) {
              break;
            }
            insertIndex++;
          }
          
          lines.splice(insertIndex, 0, importStatement.trim());
          content = lines.join('\n');
        }
      }
      
      // Scrie fișierul doar dacă s-au făcut modificări
      if (content !== originalContent) {
        await fs.writeFile(filePath, content, 'utf8');
      }
      
    } catch {
      result.errors.push(`Error processing file: ${error}`);
    }

    return result;
  }

  /**
   * Rulează fix-urile pe toate fișierele
   */
  async fixAll(): Promise<void> {
    console.log('🔍 Searching for files to fix...');
    
    const files = await this.findFiles(this.srcDir, ['.ts', '.tsx', '.js', '.jsx']);
    console.log(`📁 Found ${files.length} files to process`);
    
    console.log('🔧 Applying security fixes...');
    
    for (const file of files) {
      const result = await this.fixFile(file);
      this.results.push(result);
      
      if (result.fixes > 0) {
        console.log(`✅ ${path.relative(this.srcDir, file)}: ${result.fixes} fixes applied`);
      }
      
      if (result.errors.length > 0) {
        console.log(`❌ ${path.relative(this.srcDir, file)}: ${result.errors.join(', ')}`);
      }
    }
    
    this.printSummary();
  }

  /**
   * Afișează rezumatul fix-urilor
   */
  private printSummary(): void {
    const totalFixes = this.results.reduce((sum, result) => sum + result.fixes, 0);
    const filesWithFixes = this.results.filter(result => result.fixes > 0).length;
    const filesWithErrors = this.results.filter(result => result.errors.length > 0).length;
    
    console.log('\n📊 Summary:');
    console.log(`   Total fixes applied: ${totalFixes}`);
    console.log(`   Files modified: ${filesWithFixes}`);
    console.log(`   Files with errors: ${filesWithErrors}`);
    
    if (totalFixes > 0) {
      console.log('\n✨ Security fixes completed!');
      console.log('   Next steps:');
      console.log('   1. Review the changes');
      console.log('   2. Run tests to ensure functionality');
      console.log('   3. Run ESLint again to check remaining issues');
    }
  }

  /**
   * Rulează ESLint pentru a verifica progresul
   */
  async checkProgress(): Promise<void> {
    console.log('\n🔍 Running ESLint to check progress...');
    
    try {
      const { _stdout, _stderr } = await execAsync('npm run lint', {
        cwd: path.dirname(this.srcDir)
      });
      
      console.log('ESLint output:');
      console.log(stdout);
      
      if (stderr) {
        console.log('ESLint errors:');
        console.log(stderr);
      }
    } catch {
      console.log('ESLint found issues (_expected):');
      if (error instanceof Error && 'stdout' in error) {
        console.log((error as unknown).stdout);
      }
    }
  }
}

// Funcția principală
async function main(): Promise<void> {
  const srcDir = path.join(__dirname, '..', 'src');
  const fixer = new SecurityFixer(srcDir);
  
  console.log('🚀 Starting security fixes for frontend...');
  console.log(`📂 Source directory: ${srcDir}`);
  
  try {
    await fixer.fixAll();
    
    // Opțional: verifică progresul cu ESLint
    const shouldCheckProgress = process.argv.includes('--check-progress');
    if (shouldCheckProgress) {
      await fixer.checkProgress();
    }
    
  } catch {
    console.error('❌ Error during security fixes:', error);
    process.exit(1);
  }
}

// Rulează scriptul dacă este apelat direct
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { SecurityFixer };