// Configur<PERSON>ri pentru sistemul de autentificare

interface AuthConfig {
  ACTIVITY_CHECK_INTERVAL: number;
  INACTIVITY_TIMEOUT: number;
  TOKEN_EXPIRY_MARGIN: number;
  MAX_REFRESH_ATTEMPTS: number;
  REQUEST_TIMEOUT: number;
  STORAGE_KEYS: {
    ACCESS_TOKEN: string;
    REFRESH_TOKEN: string;
    USER: string;
    AUTH_STORAGE: string;
  };
  ERROR_MESSAGES: {
    NO_REFRESH_TOKEN: string;
    TOKEN_REFRESH_FAILED: string;
    INVALID_CREDENTIALS: string;
    NETWORK_ERROR: string;
    SESSION_EXPIRED: string;
    UNAUTHORIZED: string;
  };
  PUBLIC_ROUTES: string[];
  PROTECTED_ROUTES: string[];
}

export const AUTH_CONFIG: AuthConfig = {
  // Timpul de expirare pentru verificarea activității utilizatorului (în milisecunde)
  ACTIVITY_CHECK_INTERVAL: 60000, // 1 minut

  // Timpul de inactivitate după care utilizatorul este deconectat (în milisecunde)
  INACTIVITY_TIMEOUT: 30 * 60 * 1000, // 30 de minute

  // Marja de timp pentru verificarea expirării token-ului (în secunde)
  TOKEN_EXPIRY_MARGIN: 30,

  // Numărul maxim de încercări de refresh token
  MAX_REFRESH_ATTEMPTS: 3,

  // Timpul de așteptare pentru cererile de autentificare (în milisecunde)
  REQUEST_TIMEOUT: 10000, // 10 secunde

  // Chei pentru localStorage
  STORAGE_KEYS: {
    ACCESS_TOKEN: 'accessToken',
    REFRESH_TOKEN: 'refreshToken',
    USER: 'user',
    AUTH_STORAGE: 'auth-storage',
  },

  // Mesaje de eroare
  ERROR_MESSAGES: {
    NO_REFRESH_TOKEN: 'Nu există refresh token',
    TOKEN_REFRESH_FAILED: 'Eroare la reîmprospătarea token-ului',
    INVALID_CREDENTIALS: 'Credențiale invalide',
    NETWORK_ERROR: 'Eroare de rețea',
    SESSION_EXPIRED: 'Sesiunea a expirat',
    UNAUTHORIZED: 'Acces neautorizat',
  },

  // Rute care nu necesită autentificare
  PUBLIC_ROUTES: [
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password',
    '/verify-email',
  ],

  // Rute care necesită autentificare
  PROTECTED_ROUTES: [
    '/dashboard',
    '/profile',
    '/expenses',
    '/categories',
    '/reports',
    '/settings',
  ],
};

export default AUTH_CONFIG;

// Export tipuri pentru utilizare în alte fișiere
export type { AuthConfig };
