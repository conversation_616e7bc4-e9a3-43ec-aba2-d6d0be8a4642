import {
  ArrowLeftIcon,
  CheckIcon,
  XMarkIcon,
  StarIcon,
  // CurrencyDollarIcon, // Not used currently
  UserIcon,
  UserGroupIcon,
  BuildingOfficeIcon,
} from '@heroicons/react/24/outline';
import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';

import PublicLayout from '../../components/layout/PublicLayout';
import {
  useCreateCheckoutSession,
  usePlans,
  useCurrentSubscription,
} from '../../hooks/useSubscription';
import { useAuthStore } from '../../store/authStore';
import { safeLog } from '../../utils/safeLogger';

interface Plan {
  id: string;
  name: string;
  description: string;
  icon: React.ElementType;
  price: {
    monthly: number;
    yearly: number;
  };
  features: string[];
  limitations: string[];
  cta: string;
  popular: boolean;
  priceId?: {
    monthly: string;
    yearly: string;
  };
}

const Pricing = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [billingCycle, setBillingCycle] = useState('monthly'); // 'monthly' or 'yearly'

  // Hooks pentru autentificare și abonamente
  const { user: _user, _isAuthenticated } = useAuthStore(state => ({
    user: state.user,
    isAuthenticated: state.isAuthenticated,
  }));
  const { data: plansFromApi, isLoading: plansLoading } = usePlans();
  const { data: currentSubscription } = useCurrentSubscription();
  const createCheckoutSession = useCreateCheckoutSession();

  // Folosește planurile din API sau fallback la planurile statice
  const staticPlans = [
    {
      id: 'free',
      name: t('pricing.plans.free.name', 'Gratuit'),
      description: t('pricing.plans.free.description', 'Perfect pentru începători'),
      icon: UserIcon,
      price: {
        monthly: 0,
        yearly: 0,
      },
      features: [
        t('pricing.plans.free.feature1', 'Până la 3 conturi bancare'),
        t('pricing.plans.free.feature2', '100 tranzacții/lună'),
        t('pricing.plans.free.feature3', 'Rapoarte de bază'),
        t('pricing.plans.free.feature4', 'Suport email'),
        t('pricing.plans.free.feature5', 'Aplicație mobilă'),
      ],
      limitations: [
        t('pricing.plans.free.limitation1', 'Fără export date'),
        t('pricing.plans.free.limitation2', 'Fără analiză avansată'),
        t('pricing.plans.free.limitation3', 'Fără integrări terțe părți'),
      ],
      cta: t('pricing.plans.free.cta', 'Începeți Gratuit'),
      popular: false,
    },
    {
      id: 'personal',
      name: t('pricing.plans.personal.name', 'Personal'),
      description: t('pricing.plans.personal.description', 'Pentru utilizatori activi'),
      icon: UserIcon,
      price: {
        monthly: 9.99,
        yearly: 99.99,
      },
      features: [
        t('pricing.plans.personal.feature1', 'Conturi bancare nelimitate'),
        t('pricing.plans.personal.feature2', 'Tranzacții nelimitate'),
        t('pricing.plans.personal.feature3', 'Analiză avansată'),
        t('pricing.plans.personal.feature4', 'Export în toate formatele'),
        t('pricing.plans.personal.feature5', 'Bugetare inteligentă'),
        t('pricing.plans.personal.feature6', 'Notificări personalizate'),
        t('pricing.plans.personal.feature7', 'Suport prioritar'),
        t('pricing.plans.personal.feature8', 'Backup automat'),
      ],
      limitations: [],
      cta: t('pricing.plans.personal.cta', 'Alegeți Personal'),
      popular: true,
    },
    {
      id: 'family',
      name: t('pricing.plans.family.name', 'Familie'),
      description: t('pricing.plans.family.description', 'Pentru familii și cupluri'),
      icon: UserGroupIcon,
      price: {
        monthly: 19.99,
        yearly: 199.99,
      },
      features: [
        t('pricing.plans.family.feature1', 'Toate funcționalitățile Personal'),
        t('pricing.plans.family.feature2', 'Până la 6 utilizatori'),
        t('pricing.plans.family.feature3', 'Bugete partajate'),
        t('pricing.plans.family.feature4', 'Permisiuni granulare'),
        t('pricing.plans.family.feature5', 'Rapoarte familiale'),
        t('pricing.plans.family.feature6', 'Obiective comune'),
        t('pricing.plans.family.feature7', 'Chat integrat'),
        t('pricing.plans.family.feature8', 'Suport telefonic'),
      ],
      limitations: [],
      cta: t('pricing.plans.family.cta', 'Alegeți Familie'),
      popular: false,
    },
    {
      id: 'business',
      name: t('pricing.plans.business.name', 'Business'),
      description: t('pricing.plans.business.description', 'Pentru afaceri mici și mijlocii'),
      icon: BuildingOfficeIcon,
      price: {
        monthly: 49.99,
        yearly: 499.99,
      },
      features: [
        t('pricing.plans.business.feature1', 'Toate funcționalitățile Familie'),
        t('pricing.plans.business.feature2', 'Utilizatori nelimitați'),
        t('pricing.plans.business.feature3', 'API pentru integrări'),
        t('pricing.plans.business.feature4', 'Rapoarte avansate'),
        t('pricing.plans.business.feature5', 'Facturare automată'),
        t('pricing.plans.business.feature6', 'Contabilitate integrată'),
        t('pricing.plans.business.feature7', 'Manager dedicat'),
        t('pricing.plans.business.feature8', 'SLA garantat'),
      ],
      limitations: [],
      cta: t('pricing.plans.business.cta', 'Contactați-ne'),
      popular: false,
    },
  ];

  const displayPlans: Plan[] = useMemo(
    () => (plansFromApi?.data as unknown as Plan[]) || staticPlans,
    [plansFromApi, staticPlans],
  );

  // Funcție pentru gestionarea checkout-ului
  const handlePlanSelection = async (plan: Plan) => {
    // Dacă planul este gratuit
    if (plan.id === 'free') {
      if (!isAuthenticated) {
        navigate('/register');
        return;
      }
      // Utilizatorul este deja pe planul gratuit
      toast(t('pricing.messages.alreadyOnFreePlan', 'Sunteți deja pe planul gratuit!'));
      return;
    }

    // Pentru planul business, redirecționează către contact
    if (plan.id === 'business') {
      navigate('/contact');
      return;
    }

    // Verifică dacă utilizatorul este autentificat
    if (!isAuthenticated) {
      // Salvează planul selectat în localStorage pentru după autentificare
      localStorage.setItem(
        'selectedPlan',
        JSON.stringify({
          planId: plan.id,
          billingCycle,
        }),
      );
      navigate('/login');
      return;
    }

    // Verifică dacă utilizatorul are deja acest abonament
    if (currentSubscription?.data && currentSubscription.data.plan?.id === plan.id) {
      toast(t('pricing.messages.alreadySubscribed', 'Aveți deja acest abonament!'));
      return;
    }

    try {
      // Creează sesiunea de checkout
      await createCheckoutSession.mutateAsync({
        planId: plan.id,
        billingCycle: billingCycle as 'monthly' | 'yearly',
        successUrl: `${window.location.origin}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: window.location.href,
      });
    } catch {
      safeLog.error('Eroare la crearea checkout-ului:', error as Error);
      toast.error(
        t('pricing.messages.checkoutError', 'A apărut o eroare. Vă rugăm să încercați din nou.'),
      );
    }
  };

  // Funcție pentru obținerea textului butonului
  const getButtonText = (plan: Plan) => {
    if (plan.id === 'free') {
      return isAuthenticated
        ? t('pricing.buttons.currentPlan', 'Planul Curent')
        : t('pricing.buttons.startFree', 'Începeți Gratuit');
    }

    if (plan.id === 'business') {
      return t('pricing.buttons.contact', 'Contactați-ne');
    }

    if (currentSubscription?.data && currentSubscription.data.plan?.id === plan.id) {
      return t('pricing.buttons.currentPlan', 'Planul Curent');
    }

    return createCheckoutSession.isPending
      ? t('pricing.buttons.processing', 'Se procesează...')
      : plan.cta;
  };

  // Funcție pentru verificarea dacă butonul este dezactivat
  const isButtonDisabled = (plan: Plan) => {
    if (plan.id === 'free' && isAuthenticated) return true;
    if (currentSubscription?.data && currentSubscription.data.plan?.id === plan.id) return true;
    if (createCheckoutSession.isPending) return true;
    return false;
  };

  const faqs = [
    {
      question: t('pricing.faq.q1', 'Pot schimba planul oricând?'),
      answer: t(
        'pricing.faq.a1',
        'Da, puteți face upgrade sau downgrade oricând. Modificările se aplică imediat și facturarea se ajustează proporțional.',
      ),
    },
    {
      question: t('pricing.faq.q2', 'Există o perioadă de probă gratuită?'),
      answer: t(
        'pricing.faq.a2',
        'Da, toate planurile plătite vin cu o perioadă de probă gratuită de 14 zile. Nu este necesară cardul de credit.',
      ),
    },
    {
      question: t('pricing.faq.q3', 'Ce metode de plată acceptați?'),
      answer: t(
        'pricing.faq.a3',
        'Acceptăm toate cardurile majore (Visa, Mastercard, American Express), PayPal și transfer bancar pentru planurile Business.',
      ),
    },
    {
      question: t('pricing.faq.q4', 'Pot anula oricând?'),
      answer: t(
        'pricing.faq.a4',
        'Absolut! Nu există contracte pe termen lung. Puteți anula oricând din setările contului dvs.',
      ),
    },
    {
      question: t('pricing.faq.q5', 'Oferiti reduceri pentru studenți sau ONG-uri?'),
      answer: t(
        'pricing.faq.a5',
        'Da, oferim reduceri de 50% pentru studenți cu ID valid și reduceri speciale pentru organizații non-profit. Contactați-ne pentru detalii.',
      ),
    },
  ];

  const getPrice = (plan: Plan) => {
    const price = plan.price[billingCycle as keyof typeof plan.price];
    if (price === 0) return t('pricing.free', 'Gratuit');

    if (billingCycle === 'yearly') {
      const monthlyEquivalent = (price / 12).toFixed(2);
      return `$${monthlyEquivalent}/${t('pricing.month', 'lună')}`;
    }

    return `$${price}/${t('pricing.month', 'lună')}`;
  };

  const getSavings = (plan: Plan) => {
    if (billingCycle === 'yearly' && plan.price.yearly > 0) {
      const yearlyPrice = plan.price.yearly;
      const monthlyPrice = plan.price.monthly * 12;
      const savings = monthlyPrice - yearlyPrice;
      const percentage = Math.round((savings / monthlyPrice) * 100);
      return `${t('pricing.save', 'Economisiți')} ${percentage}%`;
    }
    return null;
  };

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center space-x-4">
              <Link
                to="/"
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 mr-2" />
                {t('common.back', 'Înapoi')}
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-3xl font-bold text-gray-900">
                {t('product.pricing.title', 'Prețuri')}
              </h1>
            </div>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl">
              {t(
                'product.pricing.subtitle',
                'Alegeți planul perfect pentru nevoile dvs. financiare. Toate planurile includ suport 24/7 și actualizări gratuite.',
              )}
            </p>
          </div>
        </div>

        {/* Billing Toggle */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {t('pricing.title', 'Planuri Simple și Transparente')}
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              {t('pricing.subtitle', 'Fără costuri ascunse. Fără surprize. Doar valoare reală.')}
            </p>

            <div className="flex items-center justify-center space-x-4">
              <span
                className={`text-sm font-medium ${billingCycle === 'monthly' ? 'text-gray-900' : 'text-gray-500'}`}
              >
                {t('pricing.monthly', 'Lunar')}
              </span>
              <button
                onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span
                className={`text-sm font-medium ${billingCycle === 'yearly' ? 'text-gray-900' : 'text-gray-500'}`}
              >
                {t('pricing.yearly', 'Anual')}
              </span>
              {billingCycle === 'yearly' && (
                <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  {t('pricing.save_up_to', 'Economisiți până la 17%')}
                </span>
              )}
            </div>
          </div>

          {/* Loading State */}
          {plansLoading && (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">
                {t('pricing.loading', 'Se încarcă planurile...')}
              </span>
            </div>
          )}

          {/* Pricing Cards */}
          {!plansLoading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {displayPlans.map(plan => {
                const IconComponent = plan.icon;
                const savings = getSavings(plan);

                return (
                  <div
                    key={plan.id}
                    className={`relative bg-white rounded-2xl shadow-sm border-2 p-8 ${
                      plan.popular
                        ? 'border-blue-500 ring-2 ring-blue-500 ring-opacity-20'
                        : 'border-gray-200 hover:border-gray-300'
                    } transition-all duration-300 hover:shadow-lg`}
                  >
                    {plan.popular && (
                      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <div className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center">
                          <StarIcon className="w-4 h-4 mr-1" />
                          {t('pricing.popular', 'Cel mai popular')}
                        </div>
                      </div>
                    )}

                    <div className="text-center mb-8">
                      <div className="bg-gray-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <IconComponent className="w-6 h-6 text-gray-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">{plan.name}</h3>
                      <p className="text-gray-600 text-sm mb-4">{plan.description}</p>

                      <div className="mb-2">
                        <span className="text-4xl font-bold text-gray-900">{getPrice(plan)}</span>
                        {plan.price[billingCycle as keyof typeof plan.price] > 0 &&
                          billingCycle === 'yearly' && (
                            <div className="text-sm text-gray-500 mt-1">
                              ${plan.price.yearly}/{t('pricing.year', 'an')}
                            </div>
                          )}
                      </div>

                      {savings && (
                        <div className="text-sm text-green-600 font-medium">{savings}</div>
                      )}
                    </div>

                    <ul className="space-y-3 mb-8">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <CheckIcon className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700 text-sm">{feature}</span>
                        </li>
                      ))}
                      {plan.limitations.map((limitation, index) => (
                        <li key={`limitation-${index}`} className="flex items-start">
                          <XMarkIcon className="w-5 h-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-500 text-sm">{limitation}</span>
                        </li>
                      ))}
                    </ul>

                    <button
                      onClick={() => handlePlanSelection(plan)}
                      disabled={isButtonDisabled(plan)}
                      className={`w-full py-3 px-4 rounded-lg font-semibold transition-colors ${
                        isButtonDisabled(plan)
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : plan.popular
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                      }`}
                    >
                      {getButtonText(plan)}
                    </button>
                  </div>
                );
              })}
            </div>
          )}

          {/* Features Comparison */}
          <div className="bg-white py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  {t('pricing.comparison.title', 'Comparați Planurile')}
                </h2>
                <p className="text-lg text-gray-600">
                  {t('pricing.comparison.subtitle', 'Vedeți exact ce obțineți cu fiecare plan')}
                </p>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-4 px-6 font-semibold text-gray-900">
                        {t('pricing.comparison.features', 'Funcționalități')}
                      </th>
                      {displayPlans.map(plan => (
                        <th
                          key={plan.id}
                          className="text-center py-4 px-6 font-semibold text-gray-900"
                        >
                          {plan.name}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    <tr>
                      <td className="py-4 px-6 text-gray-700">
                        {t('pricing.comparison.accounts', 'Conturi bancare')}
                      </td>
                      <td className="py-4 px-6 text-center text-gray-600">3</td>
                      <td className="py-4 px-6 text-center text-green-600">∞</td>
                      <td className="py-4 px-6 text-center text-green-600">∞</td>
                      <td className="py-4 px-6 text-center text-green-600">∞</td>
                    </tr>
                    <tr>
                      <td className="py-4 px-6 text-gray-700">
                        {t('pricing.comparison.transactions', 'Tranzacții/lună')}
                      </td>
                      <td className="py-4 px-6 text-center text-gray-600">100</td>
                      <td className="py-4 px-6 text-center text-green-600">∞</td>
                      <td className="py-4 px-6 text-center text-green-600">∞</td>
                      <td className="py-4 px-6 text-center text-green-600">∞</td>
                    </tr>
                    <tr>
                      <td className="py-4 px-6 text-gray-700">
                        {t('pricing.comparison.users', 'Utilizatori')}
                      </td>
                      <td className="py-4 px-6 text-center text-gray-600">1</td>
                      <td className="py-4 px-6 text-center text-gray-600">1</td>
                      <td className="py-4 px-6 text-center text-green-600">6</td>
                      <td className="py-4 px-6 text-center text-green-600">∞</td>
                    </tr>
                    <tr>
                      <td className="py-4 px-6 text-gray-700">
                        {t('pricing.comparison.support', 'Suport')}
                      </td>
                      <td className="py-4 px-6 text-center text-gray-600">Email</td>
                      <td className="py-4 px-6 text-center text-green-600">Prioritar</td>
                      <td className="py-4 px-6 text-center text-green-600">Telefonic</td>
                      <td className="py-4 px-6 text-center text-green-600">Dedicat</td>
                    </tr>
                    <tr>
                      <td className="py-4 px-6 text-gray-700">
                        {t('pricing.comparison.price', 'Preț')}
                      </td>
                      {displayPlans.map(plan => (
                        <td key={plan.id} className="text-center py-4 px-6">
                          <span className="text-2xl font-bold text-gray-900">{getPrice(plan)}</span>
                          <span className="text-gray-500 ml-1">
                            /
                            {billingCycle === 'monthly'
                              ? t('pricing.month', 'lună')
                              : t('pricing.year', 'an')}
                          </span>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="py-4 px-6 text-gray-700"></td>
                      {displayPlans.map(plan => (
                        <td key={plan.id} className="text-center py-4 px-6">
                          <button
                            onClick={() => handlePlanSelection(plan)}
                            disabled={isButtonDisabled(plan)}
                            className={`w-full py-3 px-4 rounded-lg font-semibold transition-colors ${
                              isButtonDisabled(plan)
                                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                : plan.popular
                                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                                  : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                            }`}
                          >
                            {getButtonText(plan)}
                          </button>
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-gray-50 py-16">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  {t('pricing.faq.title', 'Întrebări Frecvente')}
                </h2>
                <p className="text-lg text-gray-600">
                  {t(
                    'pricing.faq.subtitle',
                    'Răspunsuri la cele mai comune întrebări despre prețuri',
                  )}
                </p>
              </div>

              <div className="space-y-6">
                {faqs.map((faq, index) => (
                  <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">{faq.question}</h3>
                    <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="bg-blue-600 text-white py-16">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <h2 className="text-3xl font-bold mb-4">
                {t('pricing.cta.title', 'Gata să vă transformați finanțele?')}
              </h2>
              <p className="text-xl text-blue-100 mb-8">
                {t(
                  'pricing.cta.subtitle',
                  'Alăturați-vă miilor de utilizatori care și-au îmbunătățit situația financiară cu FinanceFlow.',
                )}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => {
                    const popularPlan = displayPlans.find(p => p.popular) ?? displayPlans[1];
                    if (popularPlan) {
                      handlePlanSelection(popularPlan);
                    }
                  }}
                  disabled={createCheckoutSession.isPending}
                  className={`px-8 py-3 rounded-lg font-semibold transition-colors text-center ${
                    createCheckoutSession.isPending
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-white text-blue-600 hover:bg-gray-100'
                  }`}
                >
                  {createCheckoutSession.isPending
                    ? t('pricing.buttons.processing', 'Se procesează...')
                    : t('pricing.cta.start', 'Începeți Gratuit')}
                </button>
                <Link
                  to="/contact"
                  className="border border-blue-400 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  {t('pricing.cta.contact', 'Contactați Vânzările')}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default Pricing;
