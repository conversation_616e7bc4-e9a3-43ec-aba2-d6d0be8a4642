import {
  ArrowLeftIcon,
  ChartBarIcon,
  CreditCardIcon,
  BellIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  UserGroupIcon,
  CloudIcon,
  DevicePhoneMobileIcon,
  LockClosedIcon,
  ArrowTrendingUpIcon,
} from '@heroicons/react/24/outline';
// import React from 'react'; // Not needed with new JSX transform
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import PublicLayout from '../../components/layout/PublicLayout';

const Features = () => {
  const { t } = useTranslation();

  const features = [
    {
      icon: ChartBarIcon,
      title: t('features.analytics.title', 'Analiză Avansată'),
      description: t(
        'features.analytics.description',
        'Vizualizări interactive și rapoarte detaliate pentru a înțelege mai bine finanțele dvs.',
      ),
      benefits: [
        t('features.analytics.benefit1', 'Grafice și diagrame interactive'),
        t('features.analytics.benefit2', 'Rapoarte personalizabile'),
        t('features.analytics.benefit3', 'Predicții bazate pe AI'),
        t('features.analytics.benefit4', 'Export în multiple formate'),
      ],
    },
    {
      icon: CreditCardIcon,
      title: t('features.transactions.title', 'Gestionare Tranzacții'),
      description: t(
        'features.transactions.description',
        'Urmăriți și categorizați toate tranzacțiile dvs. cu ușurință și precizie.',
      ),
      benefits: [
        t('features.transactions.benefit1', 'Categorizare automată'),
        t('features.transactions.benefit2', 'Import din bănci'),
        t('features.transactions.benefit3', 'Detectare duplicate'),
        t('features.transactions.benefit4', 'Etichete personalizate'),
      ],
    },
    {
      icon: BellIcon,
      title: t('features.notifications.title', 'Notificări Inteligente'),
      description: t(
        'features.notifications.description',
        'Rămâneți la curent cu alertele personalizate pentru buget, facturi și obiective.',
      ),
      benefits: [
        t('features.notifications.benefit1', 'Alerte buget în timp real'),
        t('features.notifications.benefit2', 'Reminder-uri facturi'),
        t('features.notifications.benefit3', 'Notificări obiective'),
        t('features.notifications.benefit4', 'Canale multiple de comunicare'),
      ],
    },
    {
      icon: ShieldCheckIcon,
      title: t('features.security.title', 'Securitate Avansată'),
      description: t(
        'features.security.description',
        'Protecția datelor dvs. financiare cu cele mai înalte standarde de securitate.',
      ),
      benefits: [
        t('features.security.benefit1', 'Criptare end-to-end'),
        t('features.security.benefit2', 'Autentificare cu doi factori'),
        t('features.security.benefit3', 'Monitorizare activitate suspectă'),
        t('features.security.benefit4', 'Backup automat securizat'),
      ],
    },
    {
      icon: CurrencyDollarIcon,
      title: t('features.budgeting.title', 'Bugetare Inteligentă'),
      description: t(
        'features.budgeting.description',
        'Creați și gestionați bugete personalizate cu recomandări bazate pe AI.',
      ),
      benefits: [
        t('features.budgeting.benefit1', 'Bugete adaptive'),
        t('features.budgeting.benefit2', 'Recomandări personalizate'),
        t('features.budgeting.benefit3', 'Urmărire obiective'),
        t('features.budgeting.benefit4', 'Analiză tendințe cheltuieli'),
      ],
    },
    {
      icon: DocumentTextIcon,
      title: t('features.reports.title', 'Rapoarte Detaliate'),
      description: t(
        'features.reports.description',
        'Generați rapoarte comprehensive pentru o înțelegere completă a situației financiare.',
      ),
      benefits: [
        t('features.reports.benefit1', 'Rapoarte lunare/anuale'),
        t('features.reports.benefit2', 'Comparații perioade'),
        t('features.reports.benefit3', 'Analiză categorii'),
        t('features.reports.benefit4', 'Export profesional'),
      ],
    },
    {
      icon: UserGroupIcon,
      title: t('features.collaboration.title', 'Colaborare Familie'),
      description: t(
        'features.collaboration.description',
        'Gestionați finanțele familiei cu conturi partajate și permisiuni granulare.',
      ),
      benefits: [
        t('features.collaboration.benefit1', 'Conturi familiale'),
        t('features.collaboration.benefit2', 'Permisiuni personalizate'),
        t('features.collaboration.benefit3', 'Bugete partajate'),
        t('features.collaboration.benefit4', 'Comunicare integrată'),
      ],
    },
    {
      icon: CloudIcon,
      title: t('features.sync.title', 'Sincronizare Cloud'),
      description: t(
        'features.sync.description',
        'Accesați datele dvs. de oriunde cu sincronizare automată și backup în cloud.',
      ),
      benefits: [
        t('features.sync.benefit1', 'Sincronizare în timp real'),
        t('features.sync.benefit2', 'Acces multi-dispozitiv'),
        t('features.sync.benefit3', 'Backup automat'),
        t('features.sync.benefit4', 'Recuperare date'),
      ],
    },
    {
      icon: DevicePhoneMobileIcon,
      title: t('features.mobile.title', 'Aplicație Mobilă'),
      description: t(
        'features.mobile.description',
        'Gestionați finanțele în mișcare cu aplicația noastră mobilă nativă.',
      ),
      benefits: [
        t('features.mobile.benefit1', 'Interface optimizată'),
        t('features.mobile.benefit2', 'Funcționalitate offline'),
        t('features.mobile.benefit3', 'Notificări push'),
        t('features.mobile.benefit4', 'Scanare bonuri'),
      ],
    },
  ];

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center space-x-4">
              <Link
                to="/"
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 mr-2" />
                {t('common.back', 'Înapoi')}
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-3xl font-bold text-gray-900">
                {t('product.features.title', 'Funcționalități')}
              </h1>
            </div>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl">
              {t(
                'product.features.subtitle',
                'Descoperiți toate funcționalitățile puternice care vă vor ajuta să vă gestionați finanțele mai eficient ca niciodată.',
              )}
            </p>
          </div>
        </div>

        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              {t('product.features.hero.title', 'Totul ce aveți nevoie pentru finanțe perfecte')}
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              {t(
                'product.features.hero.subtitle',
                'De la analiză avansată la securitate de nivel bancar - toate într-o singură platformă.',
              )}
            </p>
          </div>
        </div>

        {/* Features Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div
                  key={index}
                  className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                >
                  <div className="flex items-center mb-6">
                    <div className="bg-blue-100 p-3 rounded-lg mr-4">
                      <IconComponent className="w-8 h-8 text-blue-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">{feature.title}</h3>
                  </div>

                  <p className="text-gray-600 mb-6 leading-relaxed">{feature.description}</p>

                  <ul className="space-y-3">
                    {feature.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-start">
                        <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>
        </div>

        {/* Integration Section */}
        <div className="bg-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {t('product.features.integration.title', 'Integrări Puternice')}
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                {t(
                  'product.features.integration.subtitle',
                  'Conectați-vă cu serviciile pe care le utilizați deja pentru o experiență completă.',
                )}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-6">
                <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CreditCardIcon className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {t('product.features.integration.banking.title', 'Integrare Bancară')}
                </h3>
                <p className="text-gray-600">
                  {t(
                    'product.features.integration.banking.description',
                    'Conectare directă cu peste 1000 de bănci și instituții financiare.',
                  )}
                </p>
              </div>

              <div className="text-center p-6">
                <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <ArrowTrendingUpIcon className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {t('product.features.integration.investment.title', 'Platforme Investiții')}
                </h3>
                <p className="text-gray-600">
                  {t(
                    'product.features.integration.investment.description',
                    'Urmăriți portofoliul de investiții din toate platformele într-un singur loc.',
                  )}
                </p>
              </div>

              <div className="text-center p-6">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <LockClosedIcon className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {t('product.features.integration.security.title', 'Securitate Avansată')}
                </h3>
                <p className="text-gray-600">
                  {t(
                    'product.features.integration.security.description',
                    'Protecție de nivel bancar cu criptare și monitorizare 24/7.',
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gray-900 text-white py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-4">
              {t('product.features.cta.title', 'Gata să începeți?')}
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              {t(
                'product.features.cta.subtitle',
                'Alăturați-vă miilor de utilizatori care și-au transformat finanțele cu FinanceFlow.',
              )}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              >
                {t('product.features.cta.start', 'Începeți Gratuit')}
              </Link>
              <Link
                to="/demo"
                className="border border-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors"
              >
                {t('product.features.cta.demo', 'Vedeți Demo')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default Features;
