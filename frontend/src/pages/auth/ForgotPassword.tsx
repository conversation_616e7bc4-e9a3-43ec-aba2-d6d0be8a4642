import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import { z } from 'zod';

import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';

// Schema de validare pentru formularul de forgot password
const forgotPasswordSchema = z.object({
  email: z.string().min(1, 'Email-ul este obligatoriu').email('Email-ul nu este valid'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

const ForgotPassword: React.FC = () => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [isSubmitted, setIsSubmitted] = React.useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = async (_data: ForgotPasswordFormData) => {
    setIsLoading(true);
    try {
      // Aici ar trebui să faci request către API pentru resetarea parolei
      // await authService.forgotPassword(data.email);

      // Simulare request
      await new Promise(resolve => setTimeout(resolve, 2000));

      setIsSubmitted(true);
      toast.success('Email-ul de resetare a fost trimis!');
    } catch {
      const errorMessage =
        error instanceof Error ? error.message : 'Eroare la trimiterea email-ului';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <svg
                className="h-6 w-6 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Email trimis!
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Am trimis instrucțiunile de resetare a parolei la adresa{' '}
              <span className="font-medium text-gray-900">{getValues('email')}</span>
            </p>
            <p className="mt-4 text-center text-sm text-gray-600">
              Verifică-ți inbox-ul și urmează instrucțiunile din email.
            </p>
          </div>

          <div className="mt-8">
            <Link
              to="/login"
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Înapoi la conectare
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Resetează parola
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Introdu adresa de email asociată contului tău și îți vom trimite un link pentru
            resetarea parolei.
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div>
            <Input
              {...register('email')}
              type="email"
              label="Adresa de email"
              placeholder="<EMAIL>"
              error={errors.email?.message as string}
              autoComplete="email"
            />
          </div>

          <div>
            <Button
              type="submit"
              variant="primary"
              size="lg"
              className="w-full"
              loading={isLoading}
              disabled={isLoading}
            >
              {isLoading ? 'Se trimite...' : 'Trimite link de resetare'}
            </Button>
          </div>

          <div className="text-center">
            <Link
              to="/login"
              className="font-medium text-primary-600 hover:text-primary-500 flex items-center justify-center"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-1" />
              Înapoi la conectare
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ForgotPassword;
