import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import React, { useMemo } from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

import ChartContainer from '../../components/ui/ChartContainer';
import { useRevenueData, usePlanStats, useSubscriptionStats } from '../../hooks/useAdminData';
import { formatCurrency } from '../../utils/helpers';
import type { DashboardStats, PlanStats /* SubscriptionStats */ } from '../../types';

// Tipuri pentru Chart.js
interface TooltipContext {
  parsed: { y: number };
  label: string;
  dataset: { data: number[] };
}

// interface TickContext { // Not used currently
//   tick: { value: number };
// }

// Înregistrează componentele Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
);

interface RevenueChartsProps {
  dashboardStats?: DashboardStats | undefined;
}

const RevenueCharts: React.FC<RevenueChartsProps> = ({ dashboardStats: _dashboardStats }) => {
  const { data: _revenueStats, _isLoading } = useRevenueData();
  const { data: planStats } = usePlanStats();
  const { data: subscriptionStats } = useSubscriptionStats();
  // Generează date mock pentru ultimele 12 luni
  const monthlyRevenueData = useMemo(() => {
    const months = [];
    const revenues = [];
    const currentDate = new Date();

    for (let i = 11; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      months.push(date.toLocaleDateString('ro-RO', { month: 'short', year: '2-digit' }));

      // Simulează creșterea veniturilor cu variații
      const baseRevenue = 5000;
      const growth = (11 - i) * 200;
      const variation = Math.random() * 1000 - 500;
      revenues.push(Math.max(0, baseRevenue + growth + variation));
    }

    return { months, revenues };
  }, []);

  // Date pentru graficul de linie - venituri în timp
  const lineChartData = {
    labels: monthlyRevenueData.months,
    datasets: [
      {
        label: 'Venituri lunare',
        data: monthlyRevenueData.revenues,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
      },
    ],
  };

  const lineChartOptions: unknown = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 1,
        callbacks: {
          label(context: TooltipContext) {
            return `Venituri: ${formatCurrency(context.parsed.y)}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#6B7280',
        },
      },
      y: {
        grid: {
          color: 'rgba(107, 114, 128, 0.1)',
        },
        ticks: {
          color: '#6B7280',
          callback(value: string | number) {
            return formatCurrency(Number(value));
          },
        },
      },
    },
  };

  // Date pentru graficul cu bare - venituri pe planuri
  const planRevenueData = useMemo(() => {
    if (!planStats || !Array.isArray(planStats)) {
      return {
        labels: ['Gratuit', 'Basic', 'Premium', 'Enterprise'],
        data: [0, 2500, 4500, 8000],
        colors: ['#6B7280', '#3B82F6', '#8B5CF6', '#10B981'],
      };
    }

    return {
      labels: planStats.map((plan: PlanStats) => plan.planName),
      data: planStats.map((plan: PlanStats) => plan.revenue || 0),
      colors: ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B'],
    };
  }, [planStats]);

  const barChartData = {
    labels: planRevenueData.labels,
    datasets: [
      {
        label: 'Venituri pe plan',
        data: planRevenueData.data,
        backgroundColor: planRevenueData.colors,
        borderColor: planRevenueData.colors,
        borderWidth: 1,
        borderRadius: 8,
        borderSkipped: false,
      },
    ],
  };

  const barChartOptions: unknown = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        callbacks: {
          label(context: TooltipContext) {
            return `Venituri: ${formatCurrency(context.parsed.y)}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#6B7280',
        },
      },
      y: {
        grid: {
          color: 'rgba(107, 114, 128, 0.1)',
        },
        ticks: {
          color: '#6B7280',
          callback(value: string | number) {
            return formatCurrency(Number(value));
          },
        },
      },
    },
  };

  // Date pentru graficul circular - distribuția utilizatorilor
  const userDistributionData = useMemo(() => {
    const totalUsers = subscriptionStats?.totalSubscriptions || 100;
    const activeUsers = subscriptionStats?.activeSubscriptions || 25;
    const inactiveUsers = totalUsers - activeUsers;

    return {
      labels: ['Abonamente inactive', 'Abonamente active'],
      data: [inactiveUsers, activeUsers],
      colors: ['#6B7280', '#8B5CF6'],
    };
  }, [subscriptionStats]);

  const doughnutChartData = {
    labels: userDistributionData.labels,
    datasets: [
      {
        data: userDistributionData.data,
        backgroundColor: userDistributionData.colors,
        borderColor: '#fff',
        borderWidth: 3,
        hoverOffset: 4,
      },
    ],
  };

  const doughnutChartOptions: unknown = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          padding: 20,
          usePointStyle: true,
          color: '#6B7280',
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        callbacks: {
          label(context: TooltipContext) {
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((context.parsed.y / total) * 100).toFixed(1);
            return `${context.label}: ${context.parsed.y} (${percentage}%)`;
          },
        },
      },
    },
    cutout: '60%',
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <ChartContainer title="Evoluția veniturilor" isLoading={true} />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ChartContainer title="Venituri pe planuri" isLoading={true} />
          <ChartContainer title="Distribuția utilizatorilor" isLoading={true} />
        </div>
        <ChartContainer title="Metrici rapide" isLoading={true} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Grafic venituri în timp */}
      <ChartContainer title="Evoluția veniturilor" subtitle="Ultimele 12 luni" className="h-80">
        <Line data={lineChartData} options={lineChartOptions} />
      </ChartContainer>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Grafic venituri pe planuri */}
        <ChartContainer title="Venituri pe planuri" className="h-64">
          <Bar data={barChartData} options={barChartOptions} />
        </ChartContainer>

        {/* Grafic distribuția utilizatorilor */}
        <ChartContainer title="Distribuția utilizatorilor" className="h-64">
          <Doughnut data={doughnutChartData} options={doughnutChartOptions} />
        </ChartContainer>
      </div>

      {/* Metrici rapide */}
      <ChartContainer title="Metrici rapide">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <p className="text-2xl font-bold text-blue-600">
              {formatCurrency(subscriptionStats?.monthlyRecurringRevenue || 0)}
            </p>
            <p className="text-sm text-blue-600 font-medium">MRR</p>
            <p className="text-xs text-gray-500">Monthly Recurring Revenue</p>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <p className="text-2xl font-bold text-green-600">
              {formatCurrency((subscriptionStats?.monthlyRecurringRevenue || 0) * 12)}
            </p>
            <p className="text-sm text-green-600 font-medium">ARR</p>
            <p className="text-xs text-gray-500">Annual Recurring Revenue</p>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <p className="text-2xl font-bold text-purple-600">
              {subscriptionStats?.activeSubscriptions || 0}
            </p>
            <p className="text-sm text-purple-600 font-medium">Active</p>
            <p className="text-xs text-gray-500">Abonamente active</p>
          </div>
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <p className="text-2xl font-bold text-orange-600">
              {subscriptionStats?.conversionRate
                ? subscriptionStats.conversionRate.toFixed(1)
                : '0.0'}
              %
            </p>
            <p className="text-sm text-orange-600 font-medium">Conversion</p>
            <p className="text-xs text-gray-500">Rata de conversie</p>
          </div>
        </div>
      </ChartContainer>
    </div>
  );
};

export default RevenueCharts;
