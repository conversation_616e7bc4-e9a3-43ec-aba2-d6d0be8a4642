import { UsersIcon, ChartBarIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline';
import React from 'react';

import { AlertBanner } from '../../components/ui/AlertBanner';
import Badge from '../../components/ui/Badge';
import Card from '../../components/ui/Card';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import {
  useSystemAlerts,
  useMarkAlertAsRead,
  useAdminDashboardStats,
} from '../../hooks/useAdminData';
import { /* formatCurrency, */ formatRelativeDate } from '../../utils/helpers';
// Importăm tipul DashboardStats din types
import type { DashboardStats } from '../../types';

// Tipuri locale pentru dashboard
interface ExtendedDashboardStats extends DashboardStats {
  recentActivity?: RecentActivity[];
}

// import AdminStats from './AdminStats'; // Auto-removed: potentially unused import
// import RevenueCharts from './RevenueCharts'; // Auto-removed: potentially unused import
// import SubscriptionManager from './SubscriptionManager'; // Auto-removed: potentially unused import
// import UsersList from './UsersList'; // Auto-removed: potentially unused import

// interface SystemAlert { // Not used currently
//   id: string;
//   type: 'error' | 'warning' | 'info';
//   title: string;
//   message: string;
//   createdAt: string;
//   isRead: boolean;
// }

interface RecentActivity {
  description: string;
  user: string;
  timestamp: string;
  type: 'error' | 'success' | 'info';
}

const AdminDashboard: React.FC = () => {
  // Hook-uri pentru datele dashboard-ului
  const { data: dashboardStats, isLoading: statsLoading } = useAdminDashboardStats();
  const { data: alerts, isLoading: alertsLoading } = useSystemAlerts();
  const markAlertAsRead = useMarkAlertAsRead();

  const handleDismissAlert = (alertId: string | number): void => {
    markAlertAsRead.mutate(alertId);
  };

  const isLoading = statsLoading || alertsLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard Administrator</h1>
        <p className="text-gray-600 mt-1">Monitorizarea și gestionarea platformei FinanceFlow</p>
      </div>

      {/* Alertele sistemului */}
      {alerts && alerts.length > 0 && (
        <div className="mb-6 space-y-3">
          {alerts.map(alert => (
            <AlertBanner
              key={alert.id}
              type={alert.type}
              title={alert.title}
              message={`${alert.message} • ${formatRelativeDate(alert.createdAt)}`}
              dismissible
              onDismiss={() => handleDismissAlert(alert.id)}
              actions={[]}
            />
          ))}
        </div>
      )}

      {/* Statistici principale */}
      <AdminStats dashboardStats={dashboardStats} />

      {/* Grafice și analize */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        <RevenueCharts dashboardStats={dashboardStats} />

        {/* Activitate recentă */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activitate recentă</h3>
          <div className="space-y-4">
            {(dashboardStats as ExtendedDashboardStats)?.recentActivity?.map(
              (activity: RecentActivity, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                    <p className="text-xs text-gray-500">
                      {activity.user} • {new Date(activity.timestamp).toLocaleString('ro-RO')}
                    </p>
                  </div>
                  <Badge variant={activity.type === 'error' ? 'danger' : 'secondary'}>
                    {activity.type}
                  </Badge>
                </div>
              ),
            ) || (
              <p className="text-sm text-gray-500 text-center py-4">Nu există activitate recentă</p>
            )}
          </div>
        </Card>
      </div>

      {/* Gestionarea utilizatorilor și abonamentelor */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        <UsersList />
        <SubscriptionManager />
      </div>

      {/* Acțiuni rapide */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Acțiuni rapide</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center justify-center px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
            <UsersIcon className="h-5 w-5 mr-2" />
            Gestionează utilizatori
          </button>
          <button className="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <CurrencyDollarIcon className="h-5 w-5 mr-2" />
            Export venituri
          </button>
          <button className="flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <ChartBarIcon className="h-5 w-5 mr-2" />
            Raport complet
          </button>
        </div>
      </Card>
    </div>
  );
};

export default AdminDashboard;
