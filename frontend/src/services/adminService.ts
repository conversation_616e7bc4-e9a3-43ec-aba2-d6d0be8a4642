// import api from './api'; // Auto-removed: potentially unused import
import { safeLog } from '../utils/safeLogger';
import type {
  User,
  PaginatedResponse,
  DashboardStats,
  SubscriptionStats,
  PlanStats,
  UsageStats,
  RevenueData,
  ActivityStats,
  SystemAlert,
} from '../types';

interface GetUsersParams {
  page?: number;
  limit?: number | undefined;
  search?: string | undefined;
  status?: string | undefined;
  plan?: string | undefined;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface GetSubscriptionsParams {
  page?: number;
  limit?: number;
  status?: string;
  plan?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface GetActivityFeedParams {
  page?: number;
  limit?: number;
  type?: string | undefined;
  timeRange?: string;
}

interface ActivityFeedItem {
  id: string;
  type: string;
  description: string;
  userId?: string;
  userName?: string;
  timestamp: string;
  metadata?: Record<string, unknown>;
}

// Tipuri locale pentru serviciul admin

interface SystemConfig {
  [key: string]: unknown;
}

/**
 * Serviciu pentru funcționalitățile de administrare
 */
class AdminService {
  /**
   * Obține statisticile generale pentru dashboard-ul admin
   */
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      const response = await api.get('/admin/dashboard/stats');
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea statisticilor dashboard:', error as Error);
      throw error;
    }
  }

  /**
   * Obține statisticile de abonament
   */
  async getSubscriptionStats(): Promise<SubscriptionStats> {
    try {
      const response = await api.get('/admin/subscriptions/stats');
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea statisticilor de abonament:', error as Error);
      throw error;
    }
  }

  /**
   * Obține statisticile planurilor
   */
  async getPlanStats(): Promise<PlanStats[]> {
    try {
      const response = await api.get('/admin/plans/stats');
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea statisticilor planurilor:', error as Error);
      throw error;
    }
  }

  /**
   * Obține statisticile de utilizare
   */
  async getUsageStats(): Promise<UsageStats> {
    try {
      const response = await api.get('/admin/usage/stats');
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea statisticilor de utilizare:', error as Error);
      throw error;
    }
  }

  /**
   * Obține datele pentru graficele de venituri
   */
  async getRevenueData(period: string = '12months'): Promise<RevenueData[]> {
    try {
      const response = await api.get(`/admin/revenue/data?period=${period}`);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea datelor de venituri:', error as Error);
      throw error;
    }
  }

  /**
   * Obține lista utilizatorilor cu paginare și filtrare
   */
  async getUsers(params: GetUsersParams = {}): Promise<PaginatedResponse<User>> {
    try {
      const {
        page = 1,
        limit = 10,
        search = '',
        status = '',
        plan = '',
        sortBy = 'created_at',
        sortOrder = 'desc',
      } = params;

      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(status && { status }),
        ...(plan && { plan }),
        sortBy,
        sortOrder,
      });

      const response = await api.get(`/admin/users?${queryParams}`);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea utilizatorilor:', error as Error);
      throw error;
    }
  }

  /**
   * Obține detaliile unui utilizator
   */
  async getUserDetails(userId: string | number): Promise<User> {
    try {
      const response = await api.get(`/admin/users/${userId}`);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea detaliilor utilizatorului:', error as Error);
      throw error;
    }
  }

  /**
   * Blochează un utilizator
   */
  async blockUser(userId: string | number, reason: string = ''): Promise<User> {
    try {
      const response = await api.post(`/admin/users/${userId}/block`, { reason });
      return response.data.data;
    } catch {
      safeLog.error('Eroare la blocarea utilizatorului:', error as Error);
      throw error;
    }
  }

  /**
   * Deblochează un utilizator
   */
  async unblockUser(userId: string | number): Promise<User> {
    try {
      const response = await api.post(`/admin/users/${userId}/unblock`);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la deblocarea utilizatorului:', error as Error);
      throw error;
    }
  }

  /**
   * Obține lista abonamentelor cu paginare și filtrare
   */
  async getSubscriptions(params: GetSubscriptionsParams = {}): Promise<PaginatedResponse<any>> {
    try {
      const {
        page = 1,
        limit = 10,
        status = '',
        plan = '',
        sortBy = 'created_at',
        sortOrder = 'desc',
      } = params;

      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(status && { status }),
        ...(plan && { plan }),
        sortBy,
        sortOrder,
      });

      const response = await api.get(`/admin/subscriptions?${queryParams}`);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea abonamentelor:', error as Error);
      throw error;
    }
  }

  /**
   * Obține detaliile unui abonament
   */
  async getSubscriptionDetails(subscriptionId: string | number): Promise<unknown> {
    try {
      const response = await api.get(`/admin/subscriptions/${subscriptionId}`);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea detaliilor abonamentului:', error as Error);
      throw error;
    }
  }

  /**
   * Suspendă un abonament
   */
  async suspendSubscription(subscriptionId: string | number, reason: string = ''): Promise<unknown> {
    try {
      const response = await api.post(`/admin/subscriptions/${subscriptionId}/suspend`, { reason });
      return response.data.data;
    } catch {
      safeLog.error('Eroare la suspendarea abonamentului:', error as Error);
      throw error;
    }
  }

  /**
   * Reactivează un abonament
   */
  async reactivateSubscription(subscriptionId: string | number): Promise<unknown> {
    try {
      const response = await api.post(`/admin/subscriptions/${subscriptionId}/reactivate`);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la reactivarea abonamentului:', error as Error);
      throw error;
    }
  }

  /**
   * Anulează un abonament
   */
  async cancelSubscription(subscriptionId: string | number, reason: string = ''): Promise<unknown> {
    try {
      const response = await api.post(`/admin/subscriptions/${subscriptionId}/cancel`, { reason });
      return response.data.data;
    } catch {
      safeLog.error('Eroare la anularea abonamentului:', error as Error);
      throw error;
    }
  }

  /**
   * Sincronizează un abonament cu Stripe
   */
  async syncSubscriptionWithStripe(subscriptionId: string | number): Promise<unknown> {
    try {
      const response = await api.post(`/admin/subscriptions/${subscriptionId}/sync`);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la sincronizarea cu Stripe:', error as Error);
      throw error;
    }
  }

  /**
   * Obține activitatea recentă
   */
  async getActivityFeed(
    params: GetActivityFeedParams = {},
  ): Promise<PaginatedResponse<ActivityFeedItem>> {
    try {
      const { _page = 1, _limit = 20, _type = '', _timeRange = '7d' } = params;

      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(type && { type }),
        timeRange,
      });

      const response = await api.get(`/admin/activity?${queryParams}`);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea activității:', error as Error);
      throw error;
    }
  }

  /**
   * Obține statisticile activității
   */
  async getActivityStats(timeRange: string = '7d'): Promise<ActivityStats> {
    try {
      const response = await api.get(`/admin/activity/stats?timeRange=${timeRange}`);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea statisticilor activității:', error as Error);
      throw error;
    }
  }

  /**
   * Exportă date pentru rapoarte
   */
  async exportData(type: string, params: Record<string, unknown> = {}): Promise<Blob> {
    try {
      const queryParams = new URLSearchParams(params);
      const response = await api.get(`/admin/export/${type}?${queryParams}`, {
        responseType: 'blob',
      });
      return response.data;
    } catch {
      safeLog.error('Eroare la exportul datelor:', error as Error);
      throw error;
    }
  }

  /**
   * Obține alertele sistemului
   */
  async getSystemAlerts(): Promise<SystemAlert[]> {
    try {
      const response = await api.get('/admin/alerts');
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea alertelor:', error as Error);
      throw error;
    }
  }

  /**
   * Marchează o alertă ca citită
   */
  async markAlertAsRead(alertId: string | number): Promise<SystemAlert> {
    try {
      const response = await api.post(`/admin/alerts/${alertId}/read`);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la marcarea alertei:', error as Error);
      throw error;
    }
  }

  /**
   * Obține configurările sistemului
   */
  async getSystemConfig(): Promise<SystemConfig> {
    try {
      const response = await api.get('/admin/config');
      return response.data.data;
    } catch {
      safeLog.error('Eroare la obținerea configurărilor:', error as Error);
      throw error;
    }
  }

  /**
   * Actualizează configurările sistemului
   */
  async updateSystemConfig(config: SystemConfig): Promise<SystemConfig> {
    try {
      const response = await api.put('/admin/config', config);
      return response.data.data;
    } catch {
      safeLog.error('Eroare la actualizarea configurărilor:', error as Error);
      throw error;
    }
  }
}

export default new AdminService();
