import axios, { type AxiosInstance, type AxiosResponse, AxiosError } from 'axios';
import { createCaseInterceptors } from '../utils/caseConverter';
import { safeLog } from '../utils/safeLogger';

// Configurația de bază pentru API
const API_BASE_URL =
  typeof process !== 'undefined' && process.env && process.env['REACT_APP_API_URL']
    ? process.env['REACT_APP_API_URL']
    : 'http://localhost:3000/api';

// Creează instanța axios
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'X-Case-Format': 'camelCase', // Indică backend-ului că preferăm camelCase
  },
});

// Creează interceptorii de transformare
const caseInterceptors = createCaseInterceptors();

// Interceptor pentru request - adaugă token-ul de autentificare și transformă datele
api.interceptors.request.use(
  (_config): unknown => {
    const token = localStorage.getItem('accessToken');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Aplică transformarea de case pentru request
    return caseInterceptors.request(config);
  },
  (error: AxiosError): Promise<AxiosError> => {
    return Promise.reject(error);
  },
);

// Interceptor pentru response - gestionează erorile globale și transformă datele
api.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    // Aplică transformarea de case pentru response
    return caseInterceptors.response(response) as AxiosResponse;
  },
  (error: AxiosError): Promise<AxiosError> => {
    // Aplică transformarea de case pentru erori
    const transformedError = caseInterceptors.error(error);

    // Gestionează erorile de autentificare
    if (error.response?.status === 401) {
      // Token expirat sau invalid
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }

    // Gestionează erorile de autorizare
    if (error.response?.status === 403) {
      // Acces interzis
      safeLog.error('Acces interzis:', (error.response.data as unknown)?.message);
    }

    // Gestionează erorile de server
    if (error.response?.status && error.response.status >= 500) {
      safeLog.error('Eroare de server:', (error.response.data as unknown)?.message);
    }

    return transformedError;
  },
);

export default api;
