// Service Worker pentru caching și offline support
/// <reference lib="webworker" />

import { precacheAndRoute, cleanupOutdatedCaches } from 'workbox-precaching';
import { registerRoute } from 'workbox-routing';
import { StaleWhileRevalidate, CacheFirst, NetworkFirst } from 'workbox-strategies';
import { ExpirationPlugin } from 'workbox-expiration';
// import { BackgroundSyncPlugin } from 'workbox-background-sync'; // Not used currently
import { Queue } from 'workbox-background-sync';
import { safeLog } from './utils/safeLogger';

declare const self: ServiceWorkerGlobalScope;

// Detectează dacă suntem în modul de dezvoltare
const isDevelopment =
  !self.__WB_MANIFEST || !Array.isArray(self.__WB_MANIFEST) || self.__WB_MANIFEST.length === 0;

// Precache all static assets
// În modul de dezvoltare, __WB_MANIFEST poate să nu fie disponibil
if (self.__WB_MANIFEST && Array.isArray(self.__WB_MANIFEST)) {
  precacheAndRoute(self.__WB_MANIFEST);
} else {
  // În modul de dezvoltare, folosim o listă goală
  precacheAndRoute([]);
}

// Clean up old caches
cleanupOutdatedCaches();

// Cache API responses
registerRoute(
  ({ url }: { url: URL }) => url.pathname.startsWith('/api/'),
  new NetworkFirst({
    cacheName: 'api-cache',
    plugins: [
      new ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 5 * 60, // 5 minute
      }) as unknown,
    ],
  }),
);

// Cache images
registerRoute(
  ({ request }: { request: Request }) => request.destination === 'image',
  new CacheFirst({
    cacheName: 'images-cache',
    plugins: [
      new ExpirationPlugin({
        maxEntries: 50,
        maxAgeSeconds: 30 * 24 * 60 * 60, // 30 zile
      }) as unknown,
    ],
  }),
);

// Cache fonts
registerRoute(
  ({ request }: { request: Request }) => request.destination === 'font',
  new CacheFirst({
    cacheName: 'fonts-cache',
    plugins: [
      new ExpirationPlugin({
        maxEntries: 10,
        maxAgeSeconds: 365 * 24 * 60 * 60, // 1 year
      }) as unknown,
    ],
  }),
);

// Cache CSS and JS files
registerRoute(
  ({ request }: { request: Request }) =>
    request.destination === 'style' || request.destination === 'script',
  new StaleWhileRevalidate({
    cacheName: 'static-resources',
    plugins: [
      new ExpirationPlugin({
        maxEntries: 30,
        maxAgeSeconds: 7 * 24 * 60 * 60, // 7 days
      }) as unknown,
    ],
  }),
);

// Background sync for failed API requests
// let _bgSyncPlugin: BackgroundSyncPlugin | null = null; // Not used currently
let offlineQueue: Queue | null = null;

// Initialize background sync and queue only in production mode
if (!isDevelopment) {
  try {
    // _bgSyncPlugin = new BackgroundSyncPlugin('api-queue', {
    //   maxRetentionTime: 24 * 60, // 24 hours
    // });
  } catch {
    safeLog.warn('Failed to initialize background sync plugin:');
  }

  try {
    offlineQueue = new Queue('offline-actions', {
      onSync: async ({ queue }: { queue: unknown }) => {
        let entry;
        while ((entry = await queue.shiftRequest())) {
          try {
            await fetch(entry.request);
            safeLog.debug('Offline action synced:', entry.request.url);
          } catch {
            safeLog.error('Failed to sync offline action:', error as Error);
            await queue.unshiftRequest(entry);
            throw error;
          }
        }
      },
    });
  } catch {
    safeLog.warn('Failed to initialize offline queue:');
  }
} else {
  safeLog.debug('Development mode: Skipping background sync and queue initialization');
}

// Handle offline expense creation (only in production)
if (!isDevelopment) {
  registerRoute(
    ({ url, request }: { url: URL; request: Request }) =>
      url.pathname.startsWith('/api/expenses') && request.method === 'POST',
    async ({ request }: { request: Request }) => {
      try {
        return await fetch(request);
      } catch {
        // Add to offline queue if available
        if (offlineQueue) {
          await offlineQueue.pushRequest({ request });
        }

        // Return a response indicating offline storage
        return new Response(
          JSON.stringify({
            success: true,
            offline: true,
            message: 'Expense saved offline. Will sync when online.',
          }),
          {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          },
        );
      }
    },
  );
}

// Handle app updates
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Notify clients about updates
self.addEventListener('activate', event => {
  event.waitUntil(
    (async () => {
      // Claim all clients
      await self.clients.claim();

      // Notify clients about the update
      const clients = await self.clients.matchAll();
      clients.forEach(client => {
        client.postMessage({
          type: 'SW_ACTIVATED',
          message: 'Service Worker activated',
        });
      });
    })(),
  );
});

// Handle push notifications (for future implementation)
self.addEventListener('push', event => {
  if (!event.data) return;

  const data = event.data.json();
  const options = {
    body: data.body,
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png',
    tag: data.tag || 'default',
    data: data.data,
    actions: data.actions || [],
    requireInteraction: data.requireInteraction || false,
  };

  event.waitUntil(self.registration.showNotification(data.title, options));
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
  event.notification.close();

  if (event.action) {
    // Handle action buttons
    switch (event.action) {
      case 'view':
        event.waitUntil(self.clients.openWindow(event.notification.data?.url || '/'));
        break;
      case 'dismiss':
        // Just close the notification
        break;
    }
  } else {
    // Handle notification click
    event.waitUntil(self.clients.openWindow(event.notification.data?.url || '/'));
  }
});

// Periodic background sync (for future implementation)
self.addEventListener('periodicsync', (event: unknown) => {
  if (event.tag === 'sync-expenses') {
    event.waitUntil(syncExpenses());
  }
});

async function syncExpenses() {
  try {
    // Sync any pending offline actions if queue is available (only in production)
    if (!isDevelopment && offlineQueue) {
      await offlineQueue.replayRequests();
    }

    // Fetch latest data
    const response = await fetch('/api/expenses?limit=10');
    if (response.ok) {
      // const _data = await response.json(); // Not used currently

      // Update cache with latest data
      const cache = await caches.open('api-cache');
      await cache.put('/api/expenses?limit=10', response.clone());
    }
  } catch {
    safeLog.error('Background sync failed:', error as Error);
  }
}

// Handle install event
self.addEventListener('install', event => {
  safeLog.debug('Service Worker installing...');

  // Skip waiting to activate immediately
  event.waitUntil(self.skipWaiting());
});

// Log cache usage for debugging
self.addEventListener('fetch', event => {
  if (event.request.url.includes('/api/')) {
    safeLog.debug('SW: Handling API request:');
  }
});

// Error handling
self.addEventListener('error', event => {
  safeLog.error('Service Worker error:', event.error);
});

self.addEventListener('unhandledrejection', event => {
  safeLog.error('Service Worker unhandled rejection:', event.reason);
});

// Export for TypeScript
export {};
