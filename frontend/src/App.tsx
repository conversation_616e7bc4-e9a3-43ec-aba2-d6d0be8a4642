import React, { Suspense, lazy, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// import ProtectedRoute from './components/auth/ProtectedRoute'; // Auto-removed: potentially unused import
// import PublicRoute from './components/auth/PublicRoute'; // Auto-removed: potentially unused import
// import Layout from './components/layout/Layout'; // Auto-removed: potentially unused import
// import LoadingSpinner from './components/ui/LoadingSpinner'; // Auto-removed: potentially unused import
// import NotFound from './pages/NotFound'; // Auto-removed: potentially unused import
import { useAuthStore } from './store/authStore';

// Lazy loading pentru optimizarea performanței
const Landing = lazy(() => import('./pages/Landing'));
const Login = lazy(() => import('./pages/auth/Login'));
const Register = lazy(() => import('./pages/auth/Register'));
const ForgotPassword = lazy(() => import('./pages/auth/ForgotPassword'));
const ResetPassword = lazy(() => import('./pages/auth/ResetPassword'));
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Expenses = lazy(() => import('./pages/Expenses'));
const Categories = lazy(() => import('./pages/Categories'));
const Reports = lazy(() => import('./pages/Reports'));
const Profile = lazy(() => import('./pages/Profile'));
const Settings = lazy(() => import('./pages/Settings'));

// Pagini administrare
const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard'));
const AdminStats = lazy(() => import('./pages/admin/AdminStats'));
const RevenueCharts = lazy(() => import('./pages/admin/RevenueCharts'));
const UsersList = lazy(() => import('./pages/admin/UsersList'));
const SubscriptionManager = lazy(() => import('./pages/admin/SubscriptionManager'));
const ActivityFeed = lazy(() => import('./pages/admin/ActivityFeed'));

// Pagini legale
const Terms = lazy(() => import('./pages/legal/Terms'));
const Privacy = lazy(() => import('./pages/legal/Privacy'));
const Cookies = lazy(() => import('./pages/legal/Cookies'));

// Pagini produs
const Features = lazy(() => import('./pages/product/Features'));
const Pricing = lazy(() => import('./pages/product/Pricing'));

// Pagini suport
const Documentation = lazy(() => import('./pages/support/Documentation'));
const Contact = lazy(() => import('./pages/support/Contact'));
const Help = lazy(() => import('./pages/support/Help'));

// Componenta de loading pentru Suspense
const PageLoader: React.FC = () => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="text-center">
      <LoadingSpinner size="lg" />
      <p className="mt-4 text-gray-600">Se încarcă...</p>
    </div>
  </div>
);

// Componenta principală App
const App: React.FC = () => {
  const { _isAuthenticated, _isLoading, _initializeAuth } = useAuthStore(state => ({
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    initializeAuth: state.initializeAuth,
  }));

  // Inițializează autentificarea la pornirea aplicației
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  // Afișează loader în timpul verificării autentificării
  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="App">
      <Suspense fallback={<PageLoader />}>
        <Routes>
          {/* Rută principală - Landing page pentru toți utilizatorii */}
          <Route
            path="/"
            element={
              <PublicRoute allowAuthenticated={true}>
                <Landing />
              </PublicRoute>
            }
          />
          <Route
            path="/login"
            element={
              <PublicRoute>
                <Login />
              </PublicRoute>
            }
          />
          <Route
            path="/register"
            element={
              <PublicRoute>
                <Register />
              </PublicRoute>
            }
          />
          <Route
            path="/forgot-password"
            element={
              <PublicRoute>
                <ForgotPassword />
              </PublicRoute>
            }
          />
          <Route
            path="/reset-password"
            element={
              <PublicRoute>
                <ResetPassword />
              </PublicRoute>
            }
          />

          {/* Pagini legale - accesibile pentru toți utilizatorii */}
          <Route
            path="/terms"
            element={
              <PublicRoute allowAuthenticated={true}>
                <Terms />
              </PublicRoute>
            }
          />
          <Route
            path="/privacy"
            element={
              <PublicRoute allowAuthenticated={true}>
                <Privacy />
              </PublicRoute>
            }
          />
          <Route
            path="/cookies"
            element={
              <PublicRoute allowAuthenticated={true}>
                <Cookies />
              </PublicRoute>
            }
          />

          {/* Pagini produs - accesibile pentru toți utilizatorii */}
          <Route
            path="/features"
            element={
              <PublicRoute allowAuthenticated={true}>
                <Features />
              </PublicRoute>
            }
          />
          <Route
            path="/pricing"
            element={
              <PublicRoute allowAuthenticated={true}>
                <Pricing />
              </PublicRoute>
            }
          />

          {/* Pagini suport - accesibile pentru toți utilizatorii */}
          <Route
            path="/documentation"
            element={
              <PublicRoute allowAuthenticated={true}>
                <Documentation />
              </PublicRoute>
            }
          />
          <Route
            path="/contact"
            element={
              <PublicRoute allowAuthenticated={true}>
                <Contact />
              </PublicRoute>
            }
          />
          <Route
            path="/help"
            element={
              <PublicRoute allowAuthenticated={true}>
                <Help />
              </PublicRoute>
            }
          />

          {/* Rute protejate - accesibile doar pentru utilizatorii autentificați */}
          <Route
            path="/app"
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            {/* Redirecționează către dashboard */}
            <Route
              index
              element={
                <Navigate
                  to={
                    isAuthenticated && useAuthStore.getState().user?.role === 'admin'
                      ? '/app/admin/dashboard'
                      : '/app/dashboard'
                  }
                  replace
                />
              }
            />

            {/* Dashboard */}
            <Route path="dashboard" element={<Dashboard />} />

            {/* Cheltuieli */}
            <Route path="expenses" element={<Expenses />} />

            {/* Categorii */}
            <Route path="categories" element={<Categories />} />

            {/* Rapoarte */}
            <Route path="reports" element={<Reports />} />

            {/* Profil utilizator */}
            <Route path="profile" element={<Profile />} />

            {/* Setări */}
            <Route path="settings" element={<Settings />} />

            {/* Rute administrare - accesibile doar pentru administratori */}
            <Route
              path="admin/*"
              element={
                <ProtectedRoute roles={['admin']}>
                  <Routes>
                    <Route index element={<Navigate to="/app/admin/dashboard" replace />} />
                    <Route path="dashboard" element={<AdminDashboard />} />
                    <Route
                      path="stats"
                      element={
                        <AdminStats
                          dashboardStats={{
                            users: {
                              total: 0,
                              active: 0,
                              newThisMonth: 0,
                            },
                            subscriptions: {
                              total: 0,
                              active: 0,
                              cancelled: 0,
                            },
                            expenses: {
                              total: 0,
                              thisMonth: 0,
                            },
                            revenue: {
                              monthly: 0,
                              annual: 0,
                            },
                          }}
                        />
                      }
                    />
                    <Route
                      path="revenue"
                      element={
                        <RevenueCharts
                          dashboardStats={{
                            users: {
                              total: 0,
                              active: 0,
                              newThisMonth: 0,
                            },
                            subscriptions: {
                              total: 0,
                              active: 0,
                              cancelled: 0,
                            },
                            expenses: {
                              total: 0,
                              thisMonth: 0,
                            },
                            revenue: {
                              monthly: 0,
                              annual: 0,
                            },
                          }}
                        />
                      }
                    />
                    <Route path="users" element={<UsersList />} />
                    <Route path="subscriptions" element={<SubscriptionManager />} />
                    <Route path="activity" element={<ActivityFeed />} />
                  </Routes>
                </ProtectedRoute>
              }
            />
          </Route>

          {/* Rută pentru pagini inexistente */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Suspense>
    </div>
  );
};

export default App;
