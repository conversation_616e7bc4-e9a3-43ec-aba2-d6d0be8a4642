import React, { type ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';

import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../ui/LoadingSpinner';

interface PublicRouteProps {
  children: ReactNode;
  redirectTo?: string;
  fallback?: ReactNode;
  allowAuthenticated?: boolean;
}

/**
 * Componentă pentru rutele publice care trebuie să fie accesibile doar utilizatorilor neautentificați
 * Redirecționează utilizatorii autentificați către dashboard sau o altă pagină specificată
 */
const PublicRoute: React.FC<PublicRouteProps> = ({
  children,
  redirectTo = '/app/dashboard',
  fallback = null,
  allowAuthenticated = false,
}) => {
  const { _isAuthenticated, _isLoading, _user } = useAuthStore();
  const location = useLocation();

  // Afișează loader în timpul verificării autentificării
  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-gray-600">Verificare autentificare...</p>
          </div>
        </div>
      )
    );
  }

  // Dacă utilizatorul este autentificat și nu permitem accesul autentificat
  if (isAuthenticated && !allowAuthenticated) {
    // Determină destinația în funcție de rolul utilizatorului
    let defaultRedirect = redirectTo;
    if (user?.role === 'admin' && redirectTo === '/app/dashboard') {
      defaultRedirect = '/app/admin/dashboard';
    }

    // Verifică dacă există o destinație salvată în state
    const from = location.state?.from || defaultRedirect;

    return <Navigate to={from} replace />;
  }

  // Utilizatorul nu este autentificat sau permitem accesul autentificat
  return children;
};

interface AuthRouteProps {
  children: ReactNode;
}

/**
 * Componentă pentru rutele de autentificare (login, register, etc.)
 * Acestea sunt accesibile doar utilizatorilor neautentificați
 */
export const AuthRoute: React.FC<AuthRouteProps> = ({ children }) => {
  return <PublicRoute>{children}</PublicRoute>;
};

interface GuestRouteProps {
  children: ReactNode;
}

/**
 * Componentă pentru rutele care sunt accesibile tuturor utilizatorilor
 * indiferent de starea de autentificare (ex: pagina principală, despre noi, etc.)
 */
export const GuestRoute: React.FC<GuestRouteProps> = ({ children }) => {
  return <PublicRoute allowAuthenticated={true}>{children}</PublicRoute>;
};

interface ConditionalRenderProps {
  authenticated?: ReactNode;
  unauthenticated?: ReactNode;
  loading?: ReactNode;
}

/**
 * Componentă pentru afișarea condițională a conținutului bazat pe starea de autentificare
 */
export const ConditionalRender: React.FC<ConditionalRenderProps> = ({
  authenticated = null,
  unauthenticated = null,
  loading = null,
}) => {
  const { _isAuthenticated, _isLoading } = useAuthStore();

  if (isLoading && loading) {
    return loading;
  }

  if (isAuthenticated && authenticated) {
    return authenticated;
  }

  if (!isAuthenticated && unauthenticated) {
    return unauthenticated;
  }

  return null;
};

interface GuestStatusResult {
  isGuest: boolean;
  isLoading: boolean;
  isAuthenticated: boolean;
  canAccessAuthRoutes: boolean;
}

/**
 * Hook pentru verificarea dacă utilizatorul este guest (neautentificat)
 */
export const useGuestStatus = (): GuestStatusResult => {
  const { _isAuthenticated, _isLoading } = useAuthStore();

  return {
    isGuest: !isAuthenticated && !isLoading,
    isLoading,
    isAuthenticated,
    canAccessAuthRoutes: !isAuthenticated && !isLoading,
  };
};

interface WithPublicRouteOptions {
  redirectTo?: string;
  allowAuthenticated?: boolean;
  fallback?: ReactNode;
}

/**
 * HOC pentru rutele publice
 */
export const withPublicRoute = <P extends object>(
  Component: React.ComponentType<P>,
  options: WithPublicRouteOptions = {},
): React.FC<P> => {
  return function PublicRouteComponent(props) {
    return (
      <PublicRoute {...options}>
        <Component {...props} />
      </PublicRoute>
    );
  };
};

interface PasswordRecoveryRouteProps {
  children: ReactNode;
}

/**
 * Componentă pentru rutele de recuperare a parolei
 * Accesibilă doar utilizatorilor neautentificați
 */
export const PasswordRecoveryRoute: React.FC<PasswordRecoveryRouteProps> = ({ children }) => {
  const { user } = useAuthStore();
  const redirectTo = user?.role === 'admin' ? '/app/admin/dashboard' : '/app/dashboard';

  return <PublicRoute redirectTo={redirectTo}>{children}</PublicRoute>;
};

/**
 * Hook pentru obținerea URL-ului de redirecționare în funcție de rolul utilizatorului
 */
export const useRedirectUrl = (): string => {
  const { user } = useAuthStore();
  return user?.role === 'admin' ? '/app/admin/dashboard' : '/app/dashboard';
};

interface EmailVerificationRouteProps {
  children: ReactNode;
}

/**
 * Componentă pentru rutele de verificare a email-ului
 * Accesibilă doar utilizatorilor neautentificați
 */
export const EmailVerificationRoute: React.FC<EmailVerificationRouteProps> = ({ children }) => {
  return <PublicRoute allowAuthenticated={true}>{children}</PublicRoute>;
};

export default PublicRoute;
