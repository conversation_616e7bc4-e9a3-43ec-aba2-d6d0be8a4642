import { describe, it, expect, vi } from 'vitest';
import React from 'react';
import { render, screen, fireEvent } from '../../tests/setup';
import Button from '../ui/Button';

describe('Button Component', () => {
  it('should render with default props', () => {
    // Arrange & Act
    render(React.createElement(Button, null, 'Default Button'));

    // Assert
    const button = screen.getByRole('button', { name: /default button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center');
  });

  it('should render with primary variant', () => {
    // Arrange & Act
    render(React.createElement(Button, { variant: 'primary' }, 'Primary Button'));

    // Assert
    const button = screen.getByRole('button', { name: /primary button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('bg-primary-600', 'text-white', 'border-primary-600');
  });

  it('should render with secondary variant', () => {
    // Arrange & Act
    render(React.createElement(Button, { variant: 'secondary' }, 'Secondary Button'));

    // Assert
    const button = screen.getByRole('button', { name: /secondary button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('bg-gray-600', 'text-white', 'border-gray-600');
  });

  it('should render with danger variant', () => {
    // Arrange & Act
    render(React.createElement(Button, { variant: 'danger' }, 'Delete'));

    // Assert
    const button = screen.getByRole('button', { name: /delete/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('bg-red-600', 'text-white', 'border-red-600');
  });

  it('should apply correct styles for small size', () => {
    // Arrange & Act
    render(React.createElement(Button, { size: 'sm' }, 'Small Button'));

    // Assert
    const button = screen.getByRole('button', { name: /small button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('px-3', 'py-1.5', 'text-sm');
  });

  it('should apply correct styles for large size', () => {
    // Arrange & Act
    render(React.createElement(Button, { size: 'lg' }, 'Large Button'));

    // Assert
    const button = screen.getByRole('button', { name: /large button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('px-6', 'py-3', 'text-base');
  });

  it('should handle click events', () => {
    // Arrange
    const handleClick = vi.fn();

    // Act
    render(React.createElement(Button, { onClick: handleClick }, 'Click me'));
    fireEvent.click(screen.getByRole('button'));

    // Assert
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when disabled prop is true', () => {
    // Arrange & Act
    render(React.createElement(Button, { disabled: true }, 'Disabled Button'));

    // Assert
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('disabled:cursor-not-allowed', 'disabled:opacity-60');
  });

  it('should not call onClick when disabled', () => {
    // Arrange
    const handleClick = vi.fn();
    render(
      React.createElement(Button, { disabled: true, onClick: handleClick }, 'Disabled Button'),
    );

    // Act
    fireEvent.click(screen.getByRole('button'));

    // Assert
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('should show loading state', () => {
    // Arrange & Act
    render(React.createElement(Button, { loading: true }, 'Loading Button'));

    // Assert
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    // Loading spinner should be present
    expect(button.querySelector('svg')).toBeInTheDocument();
  });

  it('should render with custom className', () => {
    // Arrange & Act
    render(React.createElement(Button, { className: 'custom-class' }, 'Custom Button'));

    // Assert
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
    expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center');
  });

  // Test comentat - Button nu suportă prop "as"
  // it('should render as different HTML elements when "as" prop is provided', () => {
  //   // Arrange & Act
  //   render(<Button as="a" href="/test">Link Button</Button>);

  //   // Assert
  //   const link = screen.getByRole('link');
  //   expect(link).toBeInTheDocument();
  //   expect(link).toHaveAttribute('href', '/test');
  // });

  // Test comentat - Button nu suportă prop "icon" direct
  // it('should render with icon', () => {
  //   // Arrange
  //   const TestIcon = () => <span data-testid="test-icon">Icon</span>;

  //   // Act
  //   render(<Button icon={<TestIcon />}>Button with Icon</Button>);

  //   // Assert
  //   expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  //   expect(screen.getByText('Button with Icon')).toBeInTheDocument();
  // });

  // it('should render icon only when children is not provided', () => {
  //   // Arrange
  //   const TestIcon = () => <span data-testid="test-icon">Icon</span>;

  //   // Act
  //   render(<Button icon={<TestIcon />} />);

  //   // Assert
  //   expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  //   expect(screen.getByRole('button')).toHaveClass('btn-icon-only');
  // });

  // Test comentat - Button nu suportă ref în interfața sa
  // it('should forward ref correctly', () => {
  //   // Arrange
  //   const ref = vi.fn();

  //   // Act
  //   render(<Button ref={ref}>Button with Ref</Button>);

  //   // Assert
  //   expect(ref).toHaveBeenCalledWith(expect.any(HTMLButtonElement));
  // });

  it('should handle keyboard events', () => {
    // Arrange
    const handleKeyDown = vi.fn();
    render(React.createElement(Button, { onKeyDown: handleKeyDown }, 'Keyboard Button'));

    // Act
    fireEvent.keyDown(screen.getByRole('button'), { key: 'Enter' });

    // Assert
    expect(handleKeyDown).toHaveBeenCalledTimes(1);
  });

  it('should have correct accessibility attributes', () => {
    // Arrange & Act
    render(
      React.createElement(
        Button,
        {
          'aria-label': 'Custom label',
          'aria-describedby': 'description',
          type: 'submit',
        },
        'Submit',
      ),
    );

    // Assert
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Custom label');
    expect(button).toHaveAttribute('aria-describedby', 'description');
    expect(button).toHaveAttribute('type', 'submit');
  });

  it('should render full width when fullWidth prop is true', () => {
    // Arrange & Act
    render(React.createElement(Button, { fullWidth: true }, 'Full Width Button'));

    // Assert
    const button = screen.getByRole('button');
    expect(button).toHaveClass('w-full');
  });

  it('should apply correct styles for outline variant', () => {
    // Arrange & Act
    render(React.createElement(Button, { variant: 'outline' }, 'Outline Button'));

    // Assert
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-transparent', 'text-primary-600', 'border-primary-600');
  });

  it('should handle focus and blur events', () => {
    // Arrange
    const handleFocus = vi.fn();
    const handleBlur = vi.fn();
    render(
      React.createElement(Button, { onFocus: handleFocus, onBlur: handleBlur }, 'Focus Button'),
    );

    // Act
    const button = screen.getByRole('button');
    fireEvent.focus(button);
    fireEvent.blur(button);

    // Assert
    expect(handleFocus).toHaveBeenCalledTimes(1);
    expect(handleBlur).toHaveBeenCalledTimes(1);
  });
});
