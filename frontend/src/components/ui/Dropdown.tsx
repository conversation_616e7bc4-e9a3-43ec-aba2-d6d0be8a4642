import { ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline';
import React, { useState, useRef, useEffect } from 'react';

import { cn } from '../../utils/helpers';

// import LoadingSpinner from './LoadingSpinner'; // Auto-removed: potentially unused import

// Tipuri pentru Dropdown
type DropdownPosition =
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right'
  | 'left'
  | 'right';

interface DropdownProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'children'> {
  trigger: React.ReactNode;
  children: React.ReactNode;
  isOpen?: boolean;
  onToggle?: (isOpen: boolean) => void;
  position?: DropdownPosition;
  offset?: number;
  className?: string;
  menuClassName?: string;
  disabled?: boolean;
  closeOnSelect?: boolean;
}

interface DropdownItemProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  onSelect?: () => void;
  disabled?: boolean;
  active?: boolean;
  className?: string;
  icon?: React.ReactNode;
  shortcut?: string;
}

interface DropdownSeparatorProps {
  className?: string;
}

interface DropdownHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
}

interface SelectOption {
  label?: string;
  value?: unknown;
  [key: string]: unknown;
}

interface SelectProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange' | 'value'> {
  value?: unknown;
  onChange?: (value: unknown) => void;
  options?: SelectOption[];
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  error?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  multiple?: boolean;
  className?: string;
  buttonClassName?: string;
  optionClassName?: string;
  menuClassName?: string;
  renderOption?: (option: SelectOption) => React.ReactNode;
  renderValue?: (value: unknown) => React.ReactNode;
  getOptionLabel?: (option: SelectOption) => string;
  getOptionValue?: (option: SelectOption) => string | number;
  offset?: number;
}

interface ActionItem {
  type?: 'action' | 'separator' | 'header';
  label?: string;
  onClick?: () => void;
  disabled?: boolean;
  icon?: React.ReactNode;
  shortcut?: string;
}

interface ActionMenuProps extends Omit<DropdownProps, 'children'> {
  trigger: React.ReactNode;
  actions?: ActionItem[];
  position?: DropdownPosition;
  className?: string;
}

/**
 * Hook pentru detectarea click-urilor în afara componentei
 */
const useClickOutside = (ref: React.RefObject<HTMLElement>, callback: () => void) => {
  useEffect(() => {
    const handleClick = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        callback();
      }
    };

    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [ref, callback]);
};

/**
 * Hook pentru gestionarea navigării cu tastatura
 */
const useKeyboardNavigation = (
  isOpen: boolean,
  items: unknown[],
  onSelect: (item: unknown) => void,
  onClose: () => void,
): number => {
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);

  useEffect(() => {
    if (!isOpen) {
      setFocusedIndex(-1);
      return;
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setFocusedIndex(prev => (prev < items.length - 1 ? prev + 1 : 0));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setFocusedIndex(prev => (prev > 0 ? prev - 1 : items.length - 1));
          break;
        case 'Enter':
        case ' ':
          e.preventDefault();
          if (focusedIndex >= 0 && items[focusedIndex]) {
            onSelect(items[focusedIndex]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, items, focusedIndex, onSelect, onClose]);

  return focusedIndex;
};

/**
 * Componenta Dropdown de bază
 */
const Dropdown: React.FC<DropdownProps> = ({
  trigger,
  children,
  isOpen: controlledIsOpen,
  onToggle,
  position = 'bottom-left',
  offset = 8,
  className = '',
  menuClassName = '',
  disabled = false,
  closeOnSelect = true,
  ...rest
}) => {
  const [internalIsOpen, setInternalIsOpen] = useState<boolean>(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;

  const handleToggle = (): void => {
    if (disabled) return;

    if (onToggle) {
      onToggle(!isOpen);
    } else {
      setInternalIsOpen(!isOpen);
    }
  };

  const handleClose = (): void => {
    if (onToggle) {
      onToggle(false);
    } else {
      setInternalIsOpen(false);
    }
  };

  useClickOutside(dropdownRef, handleClose);

  const positionClasses: Record<DropdownPosition, string> = {
    'top-left': 'bottom-full left-0 mb-2',
    'top-right': 'bottom-full right-0 mb-2',
    'bottom-left': 'top-full left-0 mt-2',
    'bottom-right': 'top-full right-0 mt-2',
    left: 'right-full top-0 mr-2',
    right: 'left-full top-0 ml-2',
  };

  return (
    <div ref={dropdownRef} className={cn('relative inline-block', className)} {...rest}>
      {/* Trigger */}
      <div onClick={handleToggle}>{trigger}</div>

      {/* Menu */}
      {isOpen && (
        <div
          className={cn(
            'absolute z-50 min-w-full',
            'bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5',
            'transform transition-all duration-200 ease-out',
            'animate-scale-in origin-top',
            positionClasses[position],
            menuClassName,
          )}
          style={{ marginTop: position.includes('bottom') ? offset : undefined }}
        >
          <div className="py-1">
            {React.Children.map(children, child => {
              if (React.isValidElement(child)) {
                const childProps = child.props as { onSelect?: () => void };
                const newProps: { onSelect?: () => void } = {};

                if (closeOnSelect) {
                  newProps.onSelect = () => {
                    childProps.onSelect?.();
                    handleClose();
                  };
                } else if (childProps.onSelect) {
                  newProps.onSelect = childProps.onSelect;
                }

                return React.cloneElement(
                  child as React.ReactElement<{ onSelect?: () => void }>,
                  newProps,
                );
              }
              return child;
            })}
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Item pentru dropdown
 */
export const DropdownItem: React.FC<DropdownItemProps> = ({
  children,
  onClick,
  onSelect,
  disabled = false,
  active = false,
  className = '',
  icon,
  shortcut,
  ...rest
}) => {
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled) return;

    onClick?.(e);
    onSelect?.();
  };

  return (
    <button
      type="button"
      className={cn(
        'w-full text-left px-4 py-2 text-sm',
        'flex items-center justify-between',
        'transition-colors duration-150',
        disabled
          ? 'text-gray-400 cursor-not-allowed'
          : [
              'text-gray-700 hover:bg-gray-100 hover:text-gray-900',
              'focus:bg-gray-100 focus:text-gray-900 focus:outline-none',
            ],
        active && 'bg-primary-50 text-primary-700',
        className,
      )}
      onClick={handleClick}
      disabled={disabled}
      {...rest}
    >
      <div className="flex items-center gap-3 min-w-0 flex-1">
        {icon && <span className="flex-shrink-0">{icon}</span>}
        <span className="truncate">{children as React.ReactNode}</span>
      </div>

      {shortcut && (
        <span className="text-xs text-gray-400 ml-2">{shortcut as React.ReactNode}</span>
      )}
    </button>
  );
};

/**
 * Separator pentru dropdown
 */
export const DropdownSeparator: React.FC<DropdownSeparatorProps> = ({ className = '' }) => {
  return <div className={cn('border-t border-gray-100 my-1', className)} />;
};

/**
 * Header pentru dropdown
 */
export const DropdownHeader: React.FC<DropdownHeaderProps> = ({
  children,
  className = '',
  ...rest
}) => {
  return (
    <div
      className={cn(
        'px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider',
        className,
      )}
      {...rest}
    >
      {children}
    </div>
  );
};

/**
 * Select dropdown personalizat
 */
export const Select: React.FC<SelectProps> = ({
  value,
  onChange,
  options = [],
  placeholder = 'Selectează o opțiune',
  disabled = false,
  loading = false,
  error = false,
  searchable = false,
  clearable = false,
  multiple = false,
  className = '',
  buttonClassName = '',
  optionClassName = '',
  menuClassName = '',
  renderOption,
  renderValue,
  getOptionLabel = (option: SelectOption) => option?.label || option,
  getOptionValue = (option: SelectOption) => {
    if (option?.value !== undefined) {
      return typeof option.value === 'string' || typeof option.value === 'number'
        ? option.value
        : String(option.value);
    }
    return typeof option === 'string' || typeof option === 'number'
      ? option
      : JSON.stringify(option);
  },
  offset = 8,
  // ..._rest // Not used currently
}) => {
  // Nu transmitem proprietățile rest la button pentru a evita conflictele de tip
  // Acestea sunt destinate pentru containerul Dropdown

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const searchInputRef = useRef<HTMLInputElement>(null);

  const selectedOptions = multiple ? (Array.isArray(value) ? value : []) : value ? [value] : [];

  const filteredOptions =
    searchable && searchTerm
      ? ((options as SelectOption[]) || []).filter((option: unknown) =>
          // @ts-ignore - TypeScript has issues with the option type here
          getOptionLabel(option).toLowerCase().includes(searchTerm.toLowerCase()),
        )
      : options || ([] as SelectOption[]);

  const focusedIndex = useKeyboardNavigation(
    isOpen,
    filteredOptions,
    (item: unknown) => handleSelect(item as SelectOption),
    () => setIsOpen(false),
  );

  function handleSelect(option: SelectOption): void {
    if (multiple) {
      const optionValue = getOptionValue(option);
      const isSelected = selectedOptions.some(selected => getOptionValue(_selected) === optionValue);

      let newValue;
      if (isSelected) {
        newValue = selectedOptions.filter(selected => getOptionValue(selected) !== optionValue);
      } else {
        newValue = [...selectedOptions, option];
      }

      onChange?.(newValue);
    } else {
      onChange?.(option);
      setIsOpen(false);
    }

    setSearchTerm('');
  }

  const handleClear = (e: React.MouseEvent): void => {
    e.stopPropagation();
    onChange?.(multiple ? [] : null);
  };

  // const handleToggle = (): void => { // Not used currently
  //   if (disabled || loading) return;
  //
  //   setIsOpen(!isOpen);
  //
  //   if (!isOpen && searchable) {
  //     setTimeout(() => {
  //       searchInputRef.current?.focus();
  //     }, 100);
  //   }
  // };

  const displayValue = (): React.ReactNode => {
    if (renderValue) {
      return renderValue(value);
    }

    if (multiple) {
      if (selectedOptions.length === 0) {
        return placeholder;
      }
      if (selectedOptions.length === 1) {
        return getOptionLabel(selectedOptions[0]) as React.ReactNode;
      }
      return `${selectedOptions.length} opțiuni selectate`;
    }

    return value ? (getOptionLabel(value as SelectOption) as React.ReactNode) : placeholder;
  };

  const isSelected = (option: SelectOption): boolean => {
    const optionValue = getOptionValue(option);
    return selectedOptions.some(selected => getOptionValue(_selected) === optionValue);
  };

  return (
    <Dropdown
      isOpen={isOpen}
      onToggle={setIsOpen}
      className={className}
      menuClassName={menuClassName}
      offset={offset}
      trigger={
        <button
          type="button"
          className={cn(
            'relative w-full bg-white border rounded-md shadow-sm pl-3 pr-10 py-2 text-left',
            'focus:outline-none focus:ring-1 focus:border-primary-500',
            disabled
              ? 'bg-gray-50 text-gray-500 cursor-not-allowed border-gray-200'
              : 'cursor-pointer border-gray-300 hover:border-gray-400',
            error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
            buttonClassName,
          )}
          disabled={disabled || loading}
        >
          <span className={cn('block truncate', !value && 'text-gray-500')}>
            {displayValue() as React.ReactNode}
          </span>

          <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            {loading ? (
              <LoadingSpinner size="sm" />
            ) : clearable && value && !disabled ? (
              <button
                type="button"
                className="p-1 hover:bg-gray-100 rounded pointer-events-auto"
                onClick={handleClear}
              >
                <svg
                  className="w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            ) : (
              <ChevronDownIcon
                className={cn(
                  'w-5 h-5 text-gray-400 transition-transform duration-200',
                  isOpen && 'transform rotate-180',
                )}
              />
            )}
          </span>
        </button>
      }
      closeOnSelect={!multiple}
    >
      {searchable && (
        <div className="p-2 border-b border-gray-100">
          <input
            ref={searchInputRef}
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
            placeholder="Caută..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            onClick={e => e.stopPropagation()}
          />
        </div>
      )}

      <div className="max-h-60 overflow-auto">
        {filteredOptions.length === 0 ? (
          <div className="px-4 py-2 text-sm text-gray-500">
            {searchTerm ? 'Nu s-au găsit rezultate' : 'Nu există opțiuni'}
          </div>
        ) : (
          filteredOptions.map((option, index) => {
            const selected = isSelected(option);
            const focused = index === focusedIndex;

            return (
              <DropdownItem
                key={getOptionValue(option)}
                onClick={() => handleSelect(option)}
                active={focused}
                className={cn(selected && 'bg-primary-50 text-primary-700', optionClassName)}
              >
                <div className="flex items-center justify-between w-full">
                  <span>
                    {renderOption ? (
                      <>{renderOption(option) as React.ReactNode}</>
                    ) : (
                      (getOptionLabel(option) as React.ReactNode)
                    )}
                  </span>
                  {selected && <CheckIcon className="w-4 h-4 text-primary-600" />}
                </div>
              </DropdownItem>
            );
          })
        )}
      </div>
    </Dropdown>
  );
};

/**
 * Menu dropdown pentru acțiuni
 */
export const ActionMenu: React.FC<ActionMenuProps> = ({
  trigger,
  actions = [],
  position = 'bottom-right',
  className = '',
  ...rest
}) => {
  return (
    <Dropdown trigger={trigger} position={position} className={className} {...rest}>
      {actions.map((action, index) => {
        if (action.type === 'separator') {
          return <DropdownSeparator key={index} />;
        }

        if (action.type === 'header') {
          return <DropdownHeader key={index}>{action.label}</DropdownHeader>;
        }

        const itemProps: unknown = {
          key: index,
          disabled: action.disabled,
          icon: action.icon,
          shortcut: action.shortcut,
        };

        if (action.onClick) {
          itemProps.onClick = () => action.onClick!();
        }

        return (
          <DropdownItem key={index} {...itemProps}>
            {action.label}
          </DropdownItem>
        );
      })}
    </Dropdown>
  );
};

export default Dropdown;
