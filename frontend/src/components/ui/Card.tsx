import React from 'react';

import { cn } from '../../utils/helpers';

// import LoadingSpinner from './LoadingSpinner'; // Auto-removed: potentially unused import

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  shadow?: boolean;
  border?: boolean;
  rounded?: boolean;
  padding?: boolean;
  hover?: boolean;
  clickable?: boolean;
  onClick?: (e: React.MouseEvent<HTMLDivElement> | React.KeyboardEvent<HTMLDivElement>) => void;
}

interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  border?: boolean;
}

interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
  className?: string;
}

interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  padding?: boolean;
}

interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  border?: boolean;
  padding?: boolean;
}

type ChangeType = 'positive' | 'negative' | 'neutral';

interface StatsCardProps extends Omit<CardProps, 'children'> {
  title: string;
  value: string | number;
  change?: string | number;
  changeType?: ChangeType;
  icon?: React.ReactNode;
  loading?: boolean;
}

/**
 * Componenta Card de bază pentru afișarea conținutului în containere stilizate
 */
const Card: React.FC<CardProps> = ({
  children,
  className = '',
  shadow = true,
  border = true,
  rounded = true,
  padding = true,
  hover = false,
  clickable = false,
  onClick,
  ...rest
}) => {
  const isClickable = clickable || Boolean(onClick);

  return (
    <div
      className={cn(
        'bg-white',
        shadow && 'shadow-sm',
        border && 'border border-gray-200',
        rounded && 'rounded-lg',
        padding && 'p-6',
        hover && 'hover:shadow-md transition-shadow duration-200',
        isClickable && [
          'cursor-pointer',
          'hover:shadow-md hover:border-gray-300',
          'transition-all duration-200',
          'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
        ],
        className,
      )}
      onClick={onClick}
      role={isClickable ? 'button' : undefined}
      tabIndex={isClickable ? 0 : undefined}
      onKeyDown={
        isClickable
          ? (e: React.KeyboardEvent<HTMLDivElement>) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                onClick?.(e);
              }
            }
          : undefined
      }
      {...rest}
    >
      {children}
    </div>
  );
};

/**
 * Header pentru card
 */
export const CardHeader: React.FC<CardHeaderProps> = ({
  children,
  className = '',
  border = true,
  ...rest
}) => {
  return (
    <div className={cn('px-6 py-4', border && 'border-b border-gray-200', className)} {...rest}>
      {children}
    </div>
  );
};

/**
 * Titlu pentru card header
 */
export const CardTitle: React.FC<CardTitleProps> = ({
  children,
  className = '',
  as: Component = 'h3',
  ...rest
}) => {
  return (
    <Component className={cn('text-lg font-semibold text-gray-900', className)} {...rest}>
      {children}
    </Component>
  );
};

/**
 * Descriere pentru card header
 */
export const CardDescription: React.FC<CardDescriptionProps> = ({
  children,
  className = '',
  ...rest
}) => {
  return (
    <p className={cn('mt-1 text-sm text-gray-600', className)} {...rest}>
      {children}
    </p>
  );
};

/**
 * Conținutul principal al card-ului
 */
export const CardContent: React.FC<CardContentProps> = ({
  children,
  className = '',
  padding = true,
  ...rest
}) => {
  return (
    <div className={cn(padding && 'px-6 py-4', className)} {...rest}>
      {children}
    </div>
  );
};

/**
 * Footer pentru card
 */
export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  className = '',
  border = true,
  padding = true,
  ...rest
}) => {
  return (
    <div
      className={cn(border && 'border-t border-gray-200', padding && 'px-6 py-4', className)}
      {...rest}
    >
      {children}
    </div>
  );
};

/**
 * Card pentru statistici
 */
export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  changeType = 'neutral',
  icon,
  className = '',
  loading = false,
  ...rest
}) => {
  const changeColors = {
    positive: 'text-green-600',
    negative: 'text-red-600',
    neutral: 'text-gray-600',
  };

  return (
    <Card className={cn('relative overflow-hidden', className)} {...rest}>
      {loading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <LoadingSpinner size="md" />
        </div>
      )}

      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 truncate">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {change && (
            <p className={cn('text-sm mt-1 flex items-center', changeColors[changeType])}>
              {change}
            </p>
          )}
        </div>

        {icon && (
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-primary-50 rounded-lg flex items-center justify-center">
              <span className="text-primary-600">{icon}</span>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

interface ListCardProps extends Omit<CardProps, 'children'> {
  title?: string;
  description?: string;
  items?: unknown[];
  renderItem?: (item: unknown, index: number) => React.ReactNode;
  emptyMessage?: string;
  loading?: boolean;
}

/**
 * Card pentru afișarea unei liste
 */
export const ListCard: React.FC<ListCardProps> = ({
  title,
  description,
  items = [],
  renderItem,
  emptyMessage = 'Nu există elemente de afișat',
  className = '',
  loading = false,
  ...rest
}) => {
  return (
    <Card className={className} padding={false} {...rest}>
      {(title || description) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}

      <CardContent padding={false}>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="md" />
          </div>
        ) : items.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {items.map((item, index) => (
              <div key={index} className="px-6 py-4">
                {renderItem ? renderItem(item, index) : (item as React.ReactNode)}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">{emptyMessage}</div>
        )}
      </CardContent>
    </Card>
  );
};

interface FormCardProps extends CardProps {
  title?: string;
  description?: string;
}

/**
 * Card pentru formulare
 */
export const FormCard: React.FC<FormCardProps> = ({
  title,
  description,
  children,
  className = '',
  ...rest
}) => {
  return (
    <Card className={className} {...rest}>
      {(title || description) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}

      <CardContent>{children}</CardContent>
    </Card>
  );
};

interface ImageCardProps extends Omit<CardProps, 'padding'> {
  src?: string;
  alt?: string;
  title?: string;
  description?: string;
  imageClassName?: string;
}

/**
 * Card pentru imagini
 */
export const ImageCard: React.FC<ImageCardProps> = ({
  src,
  alt,
  title,
  description,
  children,
  className = '',
  imageClassName = '',
  ...rest
}) => {
  return (
    <Card className={className} padding={false} {...rest}>
      {src && (
        <div className="aspect-w-16 aspect-h-9">
          <img
            src={src}
            alt={alt}
            className={cn('w-full h-48 object-cover rounded-t-lg', imageClassName)}
          />
        </div>
      )}

      {(title || description || children) && (
        <CardContent>
          {title && <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>}
          {description && <p className="text-gray-600 mb-4">{description}</p>}
          {children}
        </CardContent>
      )}
    </Card>
  );
};

interface AlertCardProps extends Omit<CardProps, 'children'> {
  type?: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  children?: React.ReactNode;
  icon?: React.ReactNode;
  dismissible?: boolean;
  onDismiss?: () => void;
}

/**
 * Card pentru alertă/notificare
 */
export const AlertCard: React.FC<AlertCardProps> = ({
  type = 'info',
  title,
  children,
  icon,
  dismissible = false,
  onDismiss,
  className = '',
  ...rest
}) => {
  const typeStyles = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    success: 'bg-green-50 border-green-200 text-green-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    error: 'bg-red-50 border-red-200 text-red-800',
  };

  return (
    <Card className={cn(typeStyles[type], 'relative', className)} {...rest}>
      <div className="flex items-start">
        {icon && <div className="flex-shrink-0 mr-3">{icon}</div>}

        <div className="flex-1">
          {title && <h3 className="font-medium mb-1">{title}</h3>}
          {children}
        </div>

        {dismissible && (
          <button
            type="button"
            onClick={onDismiss}
            className="flex-shrink-0 ml-3 text-current opacity-70 hover:opacity-100"
            aria-label="Închide"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        )}
      </div>
    </Card>
  );
};

export default Card;
