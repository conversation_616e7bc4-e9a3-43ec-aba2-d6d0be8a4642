import { XMarkIcon } from '@heroicons/react/24/outline';
import React from 'react';

import { cn } from '../../utils/helpers';

// Tipuri pentru Badge
type BadgeVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
type BadgeSize = 'xs' | 'sm' | 'md' | 'lg';
type StatusType = 'active' | 'inactive' | 'pending' | 'completed' | 'cancelled' | 'draft' | 'published' | 'archived';
type PriorityType = 'low' | 'medium' | 'high' | 'urgent';
type SpacingType = 'tight' | 'normal' | 'loose';

interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  children?: React.ReactNode;
  variant?: BadgeVariant;
  size?: BadgeSize;
  rounded?: boolean;
  outline?: boolean;
  removable?: boolean;
  onRemove?: () => void;
  icon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  className?: string;
}

interface StatusBadgeProps extends Omit<BadgeProps, 'variant'> {
  status: StatusType;
}

interface DotBadgeProps extends BadgeProps {
  variant?: BadgeVariant;
  size?: BadgeSize;
}

interface NotificationBadgeProps extends Omit<BadgeProps, 'children'> {
  count?: number;
  max?: number;
  showZero?: boolean;
  variant?: BadgeVariant;
  size?: BadgeSize;
}

interface PriorityBadgeProps extends Omit<BadgeProps, 'variant' | 'size'> {
  priority: PriorityType;
}

interface CategoryBadgeProps extends Omit<BadgeProps, 'variant'> {
  category?: string;
  color?: string;
}

interface ProgressBadgeProps extends Omit<BadgeProps, 'children' | 'variant'> {
  progress?: number;
  total?: number;
  showPercentage?: boolean;
  variant?: BadgeVariant | 'auto';
}

interface BadgeGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  spacing?: SpacingType;
  wrap?: boolean;
}

interface TooltipBadgeProps extends BadgeProps {
  tooltip?: string;
}

/**
 * Componenta Badge de bază
 */
const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  rounded = false,
  outline = false,
  removable = false,
  onRemove,
  icon,
  rightIcon,
  className = '',
  ...rest
}) => {
  const baseClasses = [
    'inline-flex items-center font-medium',
    'transition-colors duration-200',
  ];

  const sizeClasses: Record<BadgeSize, string> = {
    xs: 'px-2 py-0.5 text-xs gap-1',
    sm: 'px-2.5 py-0.5 text-xs gap-1',
    md: 'px-3 py-1 text-sm gap-1.5',
    lg: 'px-4 py-1.5 text-base gap-2',
  };

  const roundedClasses: Record<BadgeSize, string> = {
    xs: rounded ? 'rounded-full' : 'rounded',
    sm: rounded ? 'rounded-full' : 'rounded',
    md: rounded ? 'rounded-full' : 'rounded-md',
    lg: rounded ? 'rounded-full' : 'rounded-lg',
  };

  const variantClasses: Record<BadgeVariant, string> = {
    primary: outline
      ? 'text-primary-700 bg-primary-50 ring-1 ring-inset ring-primary-600/20'
      : 'text-primary-50 bg-primary-600',
    secondary: outline
      ? 'text-gray-700 bg-gray-50 ring-1 ring-inset ring-gray-600/20'
      : 'text-gray-50 bg-gray-600',
    success: outline
      ? 'text-green-700 bg-green-50 ring-1 ring-inset ring-green-600/20'
      : 'text-green-50 bg-green-600',
    warning: outline
      ? 'text-yellow-700 bg-yellow-50 ring-1 ring-inset ring-yellow-600/20'
      : 'text-yellow-50 bg-yellow-600',
    danger: outline
      ? 'text-red-700 bg-red-50 ring-1 ring-inset ring-red-600/20'
      : 'text-red-50 bg-red-600',
    info: outline
      ? 'text-blue-700 bg-blue-50 ring-1 ring-inset ring-blue-600/20'
      : 'text-blue-50 bg-blue-600',
  };

  const iconSizes: Record<BadgeSize, string> = {
    xs: 'w-3 h-3',
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  const handleRemove = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    onRemove?.();
  };

  return (
    <span
      className={cn(
        baseClasses,
        sizeClasses[size],
        roundedClasses[size],
        variantClasses[variant],
        className,
      )}
      {...rest}
    >
      {icon && (
        <span className={cn('flex-shrink-0', iconSizes[size])}>
          {icon}
        </span>
      )}

      <span className="truncate">{children}</span>

      {rightIcon && (
        <span className={cn('flex-shrink-0', iconSizes[size])}>
          {rightIcon}
        </span>
      )}

      {removable && (
        <button
          type="button"
          className={cn(
            'flex-shrink-0 ml-1 rounded-full p-0.5',
            'hover:bg-black hover:bg-opacity-10',
            'focus:outline-none focus:bg-black focus:bg-opacity-10',
            iconSizes[size],
          )}
          onClick={handleRemove}
          aria-label="Șterge"
        >
          <XMarkIcon className="w-full h-full" />
        </button>
      )}
    </span>
  );
};

/**
 * Badge pentru status
 */
export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  children,
  className = '',
  ...rest
}) => {
  const statusConfig: Record<StatusType, { variant: BadgeVariant; children: string }> = {
    active: { variant: 'success', children: children?.toString() || 'Activ' },
    inactive: { variant: 'secondary', children: children?.toString() || 'Inactiv' },
    pending: { variant: 'warning', children: children?.toString() || 'În așteptare' },
    completed: { variant: 'success', children: children?.toString() || 'Finalizat' },
    cancelled: { variant: 'danger', children: children?.toString() || 'Anulat' },
    draft: { variant: 'secondary', children: children?.toString() || 'Ciornă' },
    published: { variant: 'primary', children: children?.toString() || 'Publicat' },
    archived: { variant: 'secondary', children: children?.toString() || 'Arhivat' },
  };

  const config = statusConfig[status] || { variant: 'secondary' as BadgeVariant, children: status };

  return (
    <Badge
      variant={config.variant}
      className={className}
      {...rest}
    >
      {config.children}
    </Badge>
  );
};

/**
 * Badge cu punct indicator
 */
export const DotBadge: React.FC<DotBadgeProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  ...rest
}) => {
  const dotSizes: Record<BadgeSize, string> = {
    xs: 'w-1.5 h-1.5',
    sm: 'w-2 h-2',
    md: 'w-2 h-2',
    lg: 'w-2.5 h-2.5',
  };

  const dotColors: Record<BadgeVariant, string> = {
    primary: 'bg-primary-600',
    secondary: 'bg-gray-600',
    success: 'bg-green-600',
    warning: 'bg-yellow-600',
    danger: 'bg-red-600',
    info: 'bg-blue-600',
  };

  return (
    <Badge
      variant={variant}
      size={size}
      outline
      className={className}
      icon={
        <span
          className={cn(
            'rounded-full',
            dotSizes[size],
            dotColors[variant],
          )}
        />
      }
      {...rest}
    >
      {children}
    </Badge>
  );
};

/**
 * Badge numeric pentru notificări
 */
export const NotificationBadge: React.FC<NotificationBadgeProps> = ({
  count = 0,
  max = 99,
  showZero = false,
  variant = 'danger',
  size = 'sm',
  className = '',
  ...rest
}) => {
  if (count === 0 && !showZero) {
    return null;
  }

  const displayCount = count > max ? `${max}+` : count.toString();

  return (
    <Badge
      variant={variant}
      size={size}
      rounded
      className={cn(
        'min-w-0 justify-center',
        size === 'xs' && 'min-w-[1rem] h-4',
        size === 'sm' && 'min-w-[1.25rem] h-5',
        size === 'md' && 'min-w-[1.5rem] h-6',
        size === 'lg' && 'min-w-[2rem] h-8',
        className,
      )}
      {...rest}
    >
      {displayCount}
    </Badge>
  );
};

/**
 * Badge pentru priorități
 */
export const PriorityBadge: React.FC<PriorityBadgeProps> = ({
  priority,
  children,
  className = '',
  ...rest
}) => {
  const priorityConfig: Record<PriorityType, { variant: BadgeVariant; children: string }> = {
    low: { variant: 'success', children: children?.toString() || 'Scăzută' },
    medium: { variant: 'warning', children: children?.toString() || 'Medie' },
    high: { variant: 'danger', children: children?.toString() || 'Ridicată' },
    urgent: { variant: 'danger', children: children?.toString() || 'Urgentă' },
  };

  const config = priorityConfig[priority] || { variant: 'secondary' as BadgeVariant, children: priority };

  return (
    <Badge
      variant={config.variant}
      size="sm"
      className={className}
      {...rest}
    >
      {config.children}
    </Badge>
  );
};

/**
 * Badge pentru categorii
 */
export const CategoryBadge: React.FC<CategoryBadgeProps> = ({
  category,
  color,
  children,
  className = '',
  ...rest
}) => {
  const customStyle: React.CSSProperties = color ? {
    backgroundColor: `${color}20`,
    color,
    borderColor: `${color}40`,
  } : {};

  return (
    <Badge
      variant="secondary"
      outline
      className={cn('border', className)}
      style={customStyle}
      {...rest}
    >
      {children || category}
    </Badge>
  );
};

/**
 * Badge pentru progres
 */
export const ProgressBadge: React.FC<ProgressBadgeProps> = ({
  progress = 0,
  total = 100,
  showPercentage = true,
  variant = 'primary',
  className = '',
  ...rest
}) => {
  const percentage = Math.round((progress / total) * 100);

  let badgeVariant: BadgeVariant = variant === 'auto' ? 'primary' : variant;
  if (variant === 'auto') {
    if (percentage >= 100) badgeVariant = 'success';
    else if (percentage >= 75) badgeVariant = 'primary';
    else if (percentage >= 50) badgeVariant = 'warning';
    else badgeVariant = 'danger';
  }

  return (
    <Badge
      variant={badgeVariant}
      outline
      className={className}
      {...rest}
    >
      {showPercentage ? `${percentage}%` : `${progress}/${total}`}
    </Badge>
  );
};

/**
 * Grup de badge-uri
 */
export const BadgeGroup: React.FC<BadgeGroupProps> = ({
  children,
  className = '',
  spacing = 'normal',
  wrap = true,
  ...rest
}) => {
  const spacingClasses: Record<SpacingType, string> = {
    tight: 'gap-1',
    normal: 'gap-2',
    loose: 'gap-3',
  };

  return (
    <div
      className={cn(
        'flex items-center',
        spacingClasses[spacing],
        wrap && 'flex-wrap',
        className,
      )}
      {...rest}
    >
      {children}
    </div>
  );
};

/**
 * Badge cu tooltip
 */
export const TooltipBadge: React.FC<TooltipBadgeProps> = ({
  children,
  tooltip,
  className = '',
  ...rest
}) => {
  return (
    <div className="relative group">
      <Badge className={className} {...rest}>
        {children}
      </Badge>

      {tooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
          {tooltip}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900" />
        </div>
      )}
    </div>
  );
};

export default Badge;