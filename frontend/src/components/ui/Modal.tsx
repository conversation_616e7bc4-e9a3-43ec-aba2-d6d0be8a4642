import { XMarkIcon } from '@heroicons/react/24/outline';
import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

import { cn } from '../../utils/helpers';

// import Button from './Button'; // Auto-removed: potentially unused import
// import LoadingSpinner from './LoadingSpinner'; // Auto-removed: potentially unused import

// Tipuri pentru Modal
type ModalSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | 'full';
type AlertType = 'info' | 'success' | 'warning' | 'error';
type ConfirmType = 'primary' | 'secondary' | 'danger' | 'success' | 'warning';
type JustifyContent = 'start' | 'center' | 'end' | 'between';

interface ModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  children: React.ReactNode;
  size?: ModalSize;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
  className?: string;
  overlayClassName?: string;
  preventBodyScroll?: boolean;
  [key: string]: unknown;
}

interface ModalHeaderProps {
  children: React.ReactNode;
  className?: string;
  border?: boolean;
  [key: string]: unknown;
}

interface ModalTitleProps {
  children: React.ReactNode;
  className?: string;
  as?: React.ElementType;
  [key: string]: unknown;
}

interface ModalDescriptionProps {
  children: React.ReactNode;
  className?: string;
  [key: string]: unknown;
}

interface ModalBodyProps {
  children: React.ReactNode;
  className?: string;
  padding?: boolean;
  [key: string]: unknown;
}

interface ModalFooterProps {
  children: React.ReactNode;
  className?: string;
  border?: boolean;
  justify?: JustifyContent;
  [key: string]: unknown;
}

interface ConfirmModalProps extends Omit<ModalProps, 'children'> {
  onConfirm?: () => void | Promise<void>;
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  type?: ConfirmType;
  loading?: boolean;
}

interface AlertModalProps extends Omit<ModalProps, 'children'> {
  title?: string;
  message?: string;
  type?: AlertType;
  buttonText?: string;
}

interface LoadingModalProps extends Omit<ModalProps, 'children' | 'onClose'> {
  message?: string;
}

interface FormModalProps extends Omit<ModalProps, 'children'> {
  onSubmit?: (e: React.FormEvent) => void | Promise<void>;
  title?: string;
  children: React.ReactNode;
  submitText?: string;
  cancelText?: string;
  loading?: boolean;
  submitDisabled?: boolean;
}

/**
 * Hook pentru gestionarea focus-ului în modal
 */
const useFocusTrap = (isOpen: boolean, modalRef: React.RefObject<HTMLDivElement>) => {
  useEffect(() => {
    if (!isOpen || !modalRef.current) return;

    const modal = modalRef.current;
    const focusableElements = modal.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
    );

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          (lastElement as HTMLElement)?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          (firstElement as HTMLElement)?.focus();
          e.preventDefault();
        }
      }
    };

    modal.addEventListener('keydown', handleTabKey);
    (firstElement as HTMLElement)?.focus();

    return () => {
      modal.removeEventListener('keydown', handleTabKey);
    };
  }, [isOpen, modalRef]);
};

/**
 * Hook pentru gestionarea scroll-ului body când modal-ul este deschis
 */
const useBodyScrollLock = (isOpen: boolean) => {
  useEffect(() => {
    if (isOpen) {
      const originalStyle = window.getComputedStyle(document.body).overflow;
      document.body.style.overflow = 'hidden';

      return () => {
        document.body.style.overflow = originalStyle;
      };
    }
    return undefined;
  }, [isOpen]);
};

/**
 * Componenta Modal de bază
 */
const Modal: React.FC<ModalProps> = ({
  isOpen = false,
  onClose,
  children,
  size = 'md',
  closeOnOverlayClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  className = '',
  overlayClassName = '',
  preventBodyScroll = true,
  ...rest
}) => {
  const modalRef = useRef(null);

  // Hooks pentru funcționalitate
  useFocusTrap(isOpen, modalRef);
  useBodyScrollLock(isOpen && preventBodyScroll);

  // Gestionarea tastei Escape
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose?.();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  if (!isOpen) return null;

  const sizeClasses: Record<ModalSize, string> = {
    xs: 'max-w-xs',
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    '6xl': 'max-w-6xl',
    full: 'max-w-full',
  };

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose?.();
    }
  };

  const modalContent = (
    <div
      className={cn(
        'fixed inset-0 z-50 flex items-center justify-center p-4',
        'bg-black bg-opacity-50 backdrop-blur-sm',
        overlayClassName,
      )}
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <div
        ref={modalRef}
        className={cn(
          'relative w-full bg-white rounded-lg shadow-xl',
          'transform transition-all duration-300 ease-out',
          'animate-scale-in',
          sizeClasses[size],
          className,
        )}
        {...rest}
      >
        {showCloseButton && (
          <button
            type="button"
            onClick={onClose}
            className={cn(
              'absolute top-4 right-4 z-10',
              'p-1 rounded-md text-gray-400 hover:text-gray-600',
              'hover:bg-gray-100 transition-colors duration-200',
              'focus:outline-none focus:ring-2 focus:ring-primary-500',
            )}
            aria-label="Închide modal"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        )}

        {children}
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

/**
 * Header pentru modal
 */
export const ModalHeader: React.FC<ModalHeaderProps> = ({
  children,
  className = '',
  border = true,
  ...rest
}) => {
  return (
    <div className={cn('px-6 py-4', border && 'border-b border-gray-200', className)} {...rest}>
      {children}
    </div>
  );
};

/**
 * Titlu pentru modal
 */
export const ModalTitle: React.FC<ModalTitleProps> = ({
  children,
  className = '',
  as: Component = 'h2',
  ...rest
}) => {
  return (
    <Component
      id="modal-title"
      className={cn('text-lg font-semibold text-gray-900', className)}
      {...rest}
    >
      {children}
    </Component>
  );
};

/**
 * Descriere pentru modal
 */
export const ModalDescription: React.FC<ModalDescriptionProps> = ({
  children,
  className = '',
  ...rest
}) => {
  return (
    <p className={cn('mt-1 text-sm text-gray-600', className)} {...rest}>
      {children}
    </p>
  );
};

/**
 * Conținutul principal al modal-ului
 */
export const ModalBody: React.FC<ModalBodyProps> = ({
  children,
  className = '',
  padding = true,
  ...rest
}) => {
  return (
    <div className={cn(padding && 'px-6 py-4', className)} {...rest}>
      {children}
    </div>
  );
};

/**
 * Footer pentru modal
 */
export const ModalFooter: React.FC<ModalFooterProps> = ({
  children,
  className = '',
  border = true,
  justify = 'end',
  ...rest
}) => {
  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
  };

  return (
    <div
      className={cn(
        'px-6 py-4 flex items-center gap-3',
        border && 'border-t border-gray-200',
        justifyClasses[justify],
        className,
      )}
      {...rest}
    >
      {children}
    </div>
  );
};

/**
 * Modal pentru confirmare
 */
export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title = 'Confirmare',
  message = 'Ești sigur că vrei să continui?',
  confirmText = 'Confirmă',
  cancelText = 'Anulează',
  type = 'danger',
  loading = false,
  ...rest
}) => {
  const handleConfirm = async () => {
    if (loading) return;
    await onConfirm?.();
  };

  return (
    <Modal
      isOpen={isOpen as boolean}
      onClose={onClose as () => void}
      size="sm"
      closeOnOverlayClick={!loading}
      closeOnEscape={!loading}
      showCloseButton={!loading}
      {...rest}
    >
      <ModalHeader>
        <ModalTitle>{title}</ModalTitle>
      </ModalHeader>

      <ModalBody>
        <p className="text-gray-600">{message}</p>
      </ModalBody>

      <ModalFooter>
        <Button
          variant="outline"
          onClick={onClose as (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void}
          disabled={loading}
        >
          {cancelText}
        </Button>
        <Button variant={type} onClick={handleConfirm} loading={loading}>
          {confirmText}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

/**
 * Modal pentru alertă
 */
export const AlertModal: React.FC<AlertModalProps> = ({
  isOpen,
  onClose,
  title = 'Alertă',
  message,
  type = 'info',
  buttonText = 'OK',
  ...rest
}) => {
  const typeIcons = {
    info: (
      <div className="w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
        <svg
          className="w-6 h-6 text-blue-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </div>
    ),
    success: (
      <div className="w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
        <svg
          className="w-6 h-6 text-green-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      </div>
    ),
    warning: (
      <div className="w-12 h-12 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center">
        <svg
          className="w-6 h-6 text-yellow-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
      </div>
    ),
    error: (
      <div className="w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
        <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </div>
    ),
  };

  return (
    <Modal isOpen={isOpen as boolean} onClose={onClose as () => void} size="sm" {...rest}>
      <ModalBody className="text-center">
        {typeIcons[type]}
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-6">{message}</p>
        <Button
          variant="primary"
          onClick={onClose as (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void}
          fullWidth
        >
          {buttonText}
        </Button>
      </ModalBody>
    </Modal>
  );
};

/**
 * Modal pentru loading
 */
export const LoadingModal: React.FC<LoadingModalProps> = ({
  isOpen,
  message = 'Se încarcă...',
  ...rest
}) => {
  return (
    <Modal
      isOpen={isOpen as boolean}
      size="sm"
      closeOnOverlayClick={false}
      closeOnEscape={false}
      showCloseButton={false}
      {...rest}
    >
      <ModalBody className="text-center py-8">
        <LoadingSpinner size="lg" className="mx-auto mb-4" />
        <p className="text-gray-600">{message}</p>
      </ModalBody>
    </Modal>
  );
};

/**
 * Modal pentru formulare
 */
export const FormModal: React.FC<FormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  title,
  children,
  submitText = 'Salvează',
  cancelText = 'Anulează',
  loading = false,
  submitDisabled = false,
  ...rest
}) => {
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (loading || submitDisabled) return;
    await onSubmit?.(e);
  };

  return (
    <Modal
      isOpen={isOpen as boolean}
      onClose={onClose as () => void}
      closeOnOverlayClick={!loading}
      closeOnEscape={!loading}
      showCloseButton={!loading}
      {...rest}
    >
      <form onSubmit={handleSubmit}>
        <ModalHeader>
          <ModalTitle>{title}</ModalTitle>
        </ModalHeader>

        <ModalBody>{children}</ModalBody>

        <ModalFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onClose as (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void}
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button type="submit" variant="primary" loading={loading} disabled={submitDisabled}>
            {submitText}
          </Button>
        </ModalFooter>
      </form>
    </Modal>
  );
};

export default Modal;
