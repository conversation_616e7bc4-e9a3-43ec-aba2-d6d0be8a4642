import React, { useState, createContext, useContext } from 'react';

import { cn } from '../../utils/helpers';

// Tipuri pentru Tabs
type TabsOrientation = 'horizontal' | 'vertical';
type TabsVariant = 'default' | 'pills' | 'underline' | 'cards';
type TabsSize = 'sm' | 'md' | 'lg';
type AnimationType = 'fade' | 'slide' | 'scale';

interface TabsContextValue {
  value: string | undefined;
  onValueChange: (value: string) => void;
  orientation: TabsOrientation;
  variant: TabsVariant;
  size: TabsSize;
}

interface TabsProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'> {
  children: React.ReactNode;
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  orientation?: TabsOrientation;
  variant?: TabsVariant;
  size?: TabsSize;
  className?: string;
}

interface TabsListProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
}

interface TabsTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  value: string;
  disabled?: boolean;
  className?: string;
  icon?: React.ReactNode;
  badge?: React.ReactNode;
}

interface TabsContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  value: string;
  className?: string;
  forceMount?: boolean;
}

interface ScrollableTabsProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  showScrollButtons?: boolean;
  scrollButtonClassName?: string;
}

interface LazyTabsContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  value: string;
  className?: string;
  loader?: React.ReactNode;
}

interface AnimatedTabsContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  value: string;
  className?: string;
  animation?: AnimationType;
}

/**
 * Context pentru gestionarea stării tab-urilor
 */
const TabsContext = createContext<TabsContextValue | undefined>(undefined);

/**
 * Hook pentru utilizarea contextului tab-urilor
 */
const useTabs = (): TabsContextValue => {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('useTabs must be used within a Tabs component');
  }
  return context;
};

/**
 * Componenta Tabs principală
 */
const Tabs: React.FC<TabsProps> = ({
  children,
  defaultValue,
  value: controlledValue,
  onValueChange,
  orientation = 'horizontal',
  variant = 'default',
  size = 'md',
  className = '',
  ...rest
}) => {
  const [internalValue, setInternalValue] = useState<string | undefined>(defaultValue);

  const value = controlledValue !== undefined ? controlledValue : internalValue;

  const handleValueChange = (newValue: string) => {
    if (onValueChange) {
      onValueChange(newValue);
    } else {
      setInternalValue(newValue);
    }
  };

  const contextValue: TabsContextValue = {
    value,
    onValueChange: handleValueChange,
    orientation,
    variant,
    size,
  };

  return (
    <TabsContext.Provider value={contextValue}>
      <div
        className={cn('w-full', orientation === 'vertical' && 'flex gap-6', className)}
        {...rest}
      >
        {children}
      </div>
    </TabsContext.Provider>
  );
};

/**
 * Lista de tab-uri
 */
export const TabsList: React.FC<TabsListProps> = ({ children, className = '', ...rest }) => {
  const { _orientation, _variant, _size } = useTabs();

  const baseClasses = ['flex', orientation === 'horizontal' ? 'flex-row' : 'flex-col'];

  const variantClasses: Record<TabsVariant, (string | boolean)[]> = {
    default: [
      'border-b border-gray-200',
      orientation === 'vertical' && 'border-b-0 border-r border-gray-200',
    ],
    pills: ['bg-gray-100 p-1 rounded-lg', orientation === 'vertical' && 'flex-col'],
    underline: [
      'border-b border-gray-200',
      orientation === 'vertical' && 'border-b-0 border-r border-gray-200',
    ],
    cards: [
      'border-b border-gray-200',
      orientation === 'vertical' && 'border-b-0 border-r border-gray-200',
    ],
  };

  const sizeClasses: Record<TabsSize, string> = {
    sm: orientation === 'horizontal' ? 'space-x-6' : 'space-y-1',
    md: orientation === 'horizontal' ? 'space-x-8' : 'space-y-2',
    lg: orientation === 'horizontal' ? 'space-x-10' : 'space-y-3',
  };

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        variant !== 'pills' && sizeClasses[size],
        orientation === 'vertical' && 'min-w-[200px]',
        className,
      )}
      role="tablist"
      aria-orientation={orientation}
      {...rest}
    >
      {children}
    </div>
  );
};

/**
 * Trigger individual pentru tab
 */
export const TabsTrigger: React.FC<TabsTriggerProps> = ({
  children,
  value: tabValue,
  disabled = false,
  className = '',
  icon,
  badge,
  ...rest
}) => {
  const { _value, _onValueChange, _orientation, _variant, _size } = useTabs();

  const isActive = value === tabValue;
  const isDisabled = disabled;

  const handleClick = () => {
    if (!isDisabled && tabValue !== undefined) {
      onValueChange(tabValue);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };

  const baseClasses = [
    'inline-flex items-center justify-center gap-2',
    'font-medium transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
    !isDisabled && 'cursor-pointer',
    isDisabled && 'cursor-not-allowed opacity-50',
  ];

  const sizeClasses: Record<TabsSize, string> = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
  };

  const variantClasses: Record<TabsVariant, (string | boolean)[]> = {
    default: [
      'border-b-2 border-transparent',
      orientation === 'vertical' && 'border-b-0 border-r-2 w-full justify-start',
      isActive
        ? 'border-primary-500 text-primary-600'
        : 'text-gray-500 hover:text-gray-700 hover:border-gray-300',
    ],
    pills: [
      'rounded-md',
      isActive
        ? 'bg-white text-gray-900 shadow-sm'
        : 'text-gray-600 hover:text-gray-900 hover:bg-white hover:bg-opacity-50',
    ],
    underline: [
      'border-b-2 border-transparent relative',
      orientation === 'vertical' && 'border-b-0 border-r-2 w-full justify-start',
      isActive ? 'text-primary-600' : 'text-gray-500 hover:text-gray-700',
      isActive &&
        orientation === 'horizontal' &&
        'after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-primary-500',
      isActive &&
        orientation === 'vertical' &&
        'after:absolute after:top-0 after:bottom-0 after:right-0 after:w-0.5 after:bg-primary-500',
    ],
    cards: [
      'border border-transparent rounded-t-lg',
      orientation === 'vertical' && 'rounded-t-none rounded-l-lg border-r-0',
      isActive ? 'border-gray-200 border-b-white bg-white' : 'text-gray-500 hover:text-gray-700',
      orientation === 'vertical' && isActive && 'border-b-gray-200 border-r-white',
    ],
  };

  return (
    <button
      type="button"
      className={cn(baseClasses, sizeClasses[size], variantClasses[variant], className)}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={isDisabled}
      role="tab"
      aria-selected={isActive}
      aria-controls={`tabpanel-${tabValue}`}
      id={`tab-${tabValue}`}
      tabIndex={isActive ? 0 : -1}
      {...rest}
    >
      {icon && <span className="flex-shrink-0">{icon}</span>}

      <span className={orientation === 'vertical' ? 'text-left' : ''}>{children}</span>

      {badge && <span className="flex-shrink-0">{badge}</span>}
    </button>
  );
};

/**
 * Conținutul unui tab
 */
export const TabsContent: React.FC<TabsContentProps> = ({
  children,
  value: tabValue,
  className = '',
  forceMount = false,
  ...rest
}) => {
  const { value } = useTabs();

  const isActive = value === tabValue;

  if (!isActive && !forceMount) {
    return null;
  }

  return (
    <div
      className={cn('mt-4 focus:outline-none', !isActive && 'hidden', className)}
      role="tabpanel"
      aria-labelledby={`tab-${tabValue}`}
      id={`tabpanel-${tabValue}`}
      tabIndex={0}
      {...rest}
    >
      {children}
    </div>
  );
};

/**
 * Tab-uri cu scroll orizontal pentru spații mici
 */
export const ScrollableTabs: React.FC<ScrollableTabsProps> = ({
  children,
  className = '',
  showScrollButtons = true,
  scrollButtonClassName = '',
  ...rest
}) => {
  const scrollRef = React.useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState<boolean>(false);
  const [canScrollRight, setCanScrollRight] = useState<boolean>(false);

  const checkScrollability = React.useCallback(() => {
    const container = scrollRef.current;
    if (container) {
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(container.scrollLeft < container.scrollWidth - container.clientWidth);
    }
  }, []);

  React.useEffect(() => {
    checkScrollability();
    const handleResize = () => checkScrollability();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [children]);

  const scrollLeft = React.useCallback(() => {
    const container = scrollRef.current;
    if (container) {
      container.scrollBy({ left: -200, behavior: 'smooth' });
    }
  }, []);

  const scrollRight = React.useCallback(() => {
    const container = scrollRef.current;
    if (container) {
      container.scrollBy({ left: 200, behavior: 'smooth' });
    }
  }, []);

  return (
    <div className={cn('relative', className)} {...rest}>
      {showScrollButtons && canScrollLeft && (
        <button
          type="button"
          className={cn(
            'absolute left-0 top-0 bottom-0 z-10 bg-gradient-to-r from-white to-transparent px-2 flex items-center',
            scrollButtonClassName,
          )}
          onClick={scrollLeft}
          aria-label="Scroll stânga"
        >
          <svg
            className="w-4 h-4 text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
      )}

      <div ref={scrollRef} className="overflow-x-auto scrollbar-hide" onScroll={checkScrollability}>
        <TabsList className="min-w-max">{children}</TabsList>
      </div>

      {showScrollButtons && canScrollRight && (
        <button
          type="button"
          className={cn(
            'absolute right-0 top-0 bottom-0 z-10 bg-gradient-to-l from-white to-transparent px-2 flex items-center',
            scrollButtonClassName,
          )}
          onClick={scrollRight}
          aria-label="Scroll dreapta"
        >
          <svg
            className="w-4 h-4 text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      )}
    </div>
  );
};

/**
 * Tab-uri cu lazy loading
 */
export const LazyTabsContent: React.FC<LazyTabsContentProps> = ({
  children,
  value: tabValue,
  className = '',
  loader = <div className="flex items-center justify-center py-8">Se încarcă...</div>,
  ...rest
}) => {
  const { value } = useTabs();
  const [hasLoaded, setHasLoaded] = useState<boolean>(false);
  const isActive = value === tabValue;

  React.useEffect(() => {
    if (isActive && !hasLoaded) {
      setHasLoaded(true);
    }
  }, [isActive, hasLoaded]);

  if (!isActive) {
    return null;
  }

  return (
    <div
      className={cn('mt-4 focus:outline-none', className)}
      role="tabpanel"
      aria-labelledby={`tab-${tabValue}`}
      id={`tabpanel-${tabValue}`}
      tabIndex={0}
      {...rest}
    >
      {hasLoaded ? children : loader}
    </div>
  );
};

/**
 * Tab-uri cu animații
 */
export const AnimatedTabsContent: React.FC<AnimatedTabsContentProps> = ({
  children,
  value: tabValue,
  className = '',
  animation = 'fade',
  ...rest
}) => {
  const { value } = useTabs();
  const isActive = value === tabValue;
  const [shouldRender, setShouldRender] = useState<boolean>(isActive);

  React.useEffect(() => {
    if (isActive) {
      setShouldRender(true);
      return undefined;
    } else {
      const timer = setTimeout(() => setShouldRender(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isActive]);

  if (!shouldRender) {
    return null;
  }

  const getAnimationClasses = (): string => {
    const baseClasses = 'transition-all duration-300';

    switch (animation) {
      case 'fade':
        return `${baseClasses} ${isActive ? 'opacity-100' : 'opacity-0'}`;
      case 'slide':
        return `${baseClasses} transform ${isActive ? 'translate-x-0' : 'translate-x-full'}`;
      case 'scale':
        return `${baseClasses} transform ${isActive ? 'scale-100' : 'scale-95'}`;
      default:
        return baseClasses;
    }
  };

  return (
    <div
      className={cn('mt-4 focus:outline-none', getAnimationClasses(), className)}
      role="tabpanel"
      aria-labelledby={`tab-${tabValue}`}
      id={`tabpanel-${tabValue}`}
      tabIndex={0}
      {...rest}
    >
      {children}
    </div>
  );
};

export default Tabs;
