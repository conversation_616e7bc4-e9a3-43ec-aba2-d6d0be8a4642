import React from 'react';

import { formatCurrency, formatNumber, /* formatPercentage, */ cn } from '../../utils/helpers';

// import Card from './Card'; // Auto-removed: potentially unused import

// Tipuri pentru StatCard
type StatType = 'number' | 'currency' | 'percentage';

interface StatCardProps {
  title: string;
  value: number | null | undefined;
  type?: StatType;
  currency?: string;
  trend?: number;
  trendLabel?: string;
  icon?: React.ComponentType<{ className?: string }>;
  className?: string;
  [key: string]: unknown;
}

/**
 * Componentă pentru afișarea unei statistici în format card
 */
export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  type = 'number',
  currency = 'RON',
  trend,
  trendLabel,
  icon: Icon,
  className,
  ...props
}) => {
  const formatValue = (val: number | null | undefined, type: StatType): string => {
    if (val === null || val === undefined) return '-';

    switch (type) {
      case 'currency':
        return formatCurrency(val, currency);
      case 'percentage':
        return `${val}%`;
      case 'number':
      default:
        return formatNumber(val);
    }
  };

  const getTrendColor = (trend?: number): string => {
    if (!trend) return '';
    return trend > 0 ? 'text-green-600' : trend < 0 ? 'text-red-600' : 'text-gray-600';
  };

  const getTrendIcon = (trend?: number): React.ReactNode => {
    if (!trend) return null;
    if (trend > 0) {
      return (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 17l9.2-9.2M17 17V7m0 10H7"
          />
        </svg>
      );
    }
    if (trend < 0) {
      return (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17 7l-9.2 9.2M7 7v10m0-10h10"
          />
        </svg>
      );
    }
    return (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
      </svg>
    );
  };

  return (
    <Card className={cn('p-6', className)} {...props}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{formatValue(value, type)}</p>
          {trend !== undefined && (
            <div className={cn('flex items-center mt-2 text-sm', getTrendColor(trend))}>
              {getTrendIcon(trend)}
              <span className="ml-1">
                {Math.abs(trend)}%
                {trendLabel && <span className="ml-1 text-gray-600">{trendLabel}</span>}
              </span>
            </div>
          )}
        </div>
        {Icon && (
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Icon className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default StatCard;
