import React from 'react';
import { Link } from 'react-router-dom';

import { cn } from '../../utils/helpers';

// Types
type LogoSize = 'sm' | 'md' | 'lg' | 'xl';

interface LogoProps {
  size?: LogoSize;
  showText?: boolean;
  className?: string;
  to?: string;
  animated?: boolean;
}

interface SizeConfig {
  container: string;
  text: string;
}

// Componenta pentru iconul logo-ului
const LogoIcon: React.FC<{ size: LogoSize; animated: boolean }> = ({ size, animated }) => {
  const sizeClasses: Record<LogoSize, SizeConfig> = {
    sm: {
      container: 'w-6 h-6',
      text: 'text-sm font-semibold',
    },
    md: {
      container: 'w-8 h-8',
      text: 'text-base font-semibold',
    },
    lg: {
      container: 'w-12 h-12',
      text: 'text-lg font-bold',
    },
    xl: {
      container: 'w-16 h-16',
      text: 'text-2xl font-bold',
    },
  };

  return (
    <div
      className={cn(
        'bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center shadow-lg',
        sizeClasses[size].container,
        animated && 'transition-all duration-300 hover:scale-110 hover:shadow-xl',
      )}
    >
      <svg
        viewBox="0 0 24 24"
        fill="none"
        className={cn(
          'text-white',
          size === 'sm'
            ? 'w-3 h-3'
            : size === 'md'
              ? 'w-4 h-4'
              : size === 'lg'
                ? 'w-6 h-6'
                : 'w-8 h-8',
        )}
      >
        <path
          d="M12 2C13.1 2 14 2.9 14 4V6H16C17.1 6 18 6.9 18 8V18C18 19.1 17.1 20 16 20H8C6.9 20 6 19.1 6 18V8C6 6.9 6.9 6 8 6H10V4C10 2.9 10.9 2 12 2ZM12 4V6H12V4ZM8 8V18H16V8H8ZM10 10H14V12H10V10ZM10 14H14V16H10V14Z"
          fill="currentColor"
        />
        <circle cx="12" cy="13" r="1.5" fill="currentColor" opacity="0.7" />
      </svg>
    </div>
  );
};

/**
 * Componenta Logo pentru aplicația FinanceNinja
 */
const Logo: React.FC<LogoProps> = ({
  size = 'md',
  showText = true,
  className = '',
  to = '/',
  animated = false,
}) => {
  const sizeClasses: Record<LogoSize, SizeConfig> = {
    sm: {
      container: 'w-6 h-6',
      text: 'text-sm font-semibold',
    },
    md: {
      container: 'w-8 h-8',
      text: 'text-lg font-bold',
    },
    lg: {
      container: 'w-12 h-12',
      text: 'text-xl font-bold',
    },
    xl: {
      container: 'w-16 h-16',
      text: 'text-2xl font-bold',
    },
  };

  const content = (
    <div className={cn('flex items-center space-x-3 group cursor-pointer', className)}>
      <LogoIcon size={size} animated={animated} />
      {showText && (
        <div className="flex flex-col">
          <span
            className={cn(
              'text-gray-900 group-hover:text-primary-600 transition-colors duration-200',
              sizeClasses[size].text,
            )}
          >
            FinanceNinja
          </span>
          {size === 'lg' || size === 'xl' ? (
            <span className="text-xs text-gray-500 -mt-1">Smart Expense Tracker</span>
          ) : null}
        </div>
      )}
    </div>
  );

  if (to) {
    return (
      <Link to={to} className="inline-block">
        {content}
      </Link>
    );
  }

  return content;
};

export default Logo;
