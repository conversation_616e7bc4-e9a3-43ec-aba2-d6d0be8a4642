import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { initReactI18next } from 'react-i18next';

// Import traducerile
// import enTranslation from './locales/en/translation.json'; // Auto-removed: potentially unused import
// import roTranslation from './locales/ro/translation.json'; // Auto-removed: potentially unused import

// Tipuri pentru resurse
interface TranslationResource {
  translation: Record<string, unknown>;
}

interface Resources {
  [key: string]: TranslationResource;
}

// Configurarea resurselor de traducere
const resources: Resources = {
  ro: {
    translation: roTranslation,
  },
  en: {
    translation: enTranslation,
  },
};

// Configurarea i18next
(i18n as unknown)
  // Detectează limba browserului
  .use(LanguageDetector)
  // Conectează cu React
  .use(initReactI18next)
  // Inițializează i18next
  .init({
    resources,

    // Limba implicită
    fallbackLng: 'ro' as const,

    // Limba implicită pentru dezvoltare
    lng: 'ro',

    // Configurări pentru detectarea limbii
    detection: {
      // Ordinea de detectare a limbii
      order: ['localStorage', 'navigator', 'htmlTag'],

      // Cache limba selectată în localStorage
      caches: ['localStorage'],

      // Cheia pentru localStorage
      lookupLocalStorage: 'i18nextLng',
    },

    // Configurări de debug (doar în dezvoltare)
    debug: process.env['NODE_ENV'] === 'development',

    // Configurări de interpolation
    interpolation: {
      // React face deja escape la valori
      escapeValue: false,
    },

    // Configurări pentru React
    react: {
      // Folosește Suspense pentru loading
      useSuspense: false,
    },
  });

export default i18n;

// Export hook-uri utile
export const changeLanguage = (lng: string) => {
  return i18n.changeLanguage(lng);
};

export const getCurrentLanguage = (): string => {
  return i18n.language;
};

export const getSupportedLanguages = (): string[] => {
  return Object.keys(resources);
};
