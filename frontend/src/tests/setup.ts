import '@testing-library/jest-dom';
import React, { type ReactElement } from 'react';
import { cleanup, render, type RenderOptions } from '@testing-library/react';
import { afterEach, beforeAll, vi } from 'vitest';

// Cleanup după fiecare test
afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

// Setup global înainte de toate testele
beforeAll(() => {
  // Mock pentru window.matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  // Mock pentru ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock pentru IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock pentru localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  });

  // Mock pentru sessionStorage
  const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
  });

  // Mock pentru fetch
  global.fetch = vi.fn();

  // Mock pentru URL.createObjectURL
  global.URL.createObjectURL = vi.fn();
  global.URL.revokeObjectURL = vi.fn();

  // Mock pentru console methods în teste
  global.console = {
    ...console,
    log: vi.fn(),
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };
});

// Mock pentru react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = (await vi.importActual('react-router-dom')) as Record<string, unknown>;
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      pathname: '/',
      search: '',
      hash: '',
      state: null,
    }),
    useParams: () => ({}),
  };
});

// Mock pentru react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: vi.fn(),
      language: 'ro',
    },
  }),
  Trans: ({ children }: { children: React.ReactNode }) => children,
  initReactI18next: {
    type: '3rdParty',
    init: vi.fn(),
  },
}));

// Mock pentru Chart.js
vi.mock('chart.js', () => ({
  Chart: {
    register: vi.fn(),
  },
  CategoryScale: vi.fn(),
  LinearScale: vi.fn(),
  BarElement: vi.fn(),
  Title: vi.fn(),
  Tooltip: vi.fn(),
  Legend: vi.fn(),
  ArcElement: vi.fn(),
  PointElement: vi.fn(),
  LineElement: vi.fn(),
}));

// Mock pentru react-chartjs-2
vi.mock('react-chartjs-2', () => ({
  Bar: vi.fn(() => React.createElement('div', { 'data-testid': 'bar-chart' })),
  Doughnut: vi.fn(() => React.createElement('div', { 'data-testid': 'doughnut-chart' })),
  Line: vi.fn(() => React.createElement('div', { 'data-testid': 'line-chart' })),
}));

// Mock pentru date-fns
vi.mock('date-fns', async () => {
  const actual = (await vi.importActual('date-fns')) as Record<string, unknown>;
  return {
    ...actual,
    format: vi.fn((_date, formatStr) => `formatted-${formatStr}`),
    parseISO: vi.fn(dateStr => new Date(dateStr)),
    isValid: vi.fn(() => true),
  };
});

// Test utilities
export const createMockUser = () => ({
  id: 'usr_1',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'user' as const,
  planType: 'free' as const,
  subscriptionStatus: 'active' as const,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  avatar: '',
  lastLogin: new Date('2024-01-01T00:00:00Z'),
  emailVerified: true,
  currency: 'RON',
  timezone: 'Europe/Bucharest',
  isActive: true,
  loginCount: 0,
  lastUsageReset: new Date('2024-01-01T00:00:00Z'),
  monthlyExpenseCount: 0,
  monthlyExpenseLimit: 50,
  preferences: {
    language: 'ro' as const,
    currency: 'RON' as const,
    dateFormat: 'DD/MM/YYYY' as const,
    theme: 'light' as const,
    notifications: {
      email: true,
      push: true,
      weeklyReports: true,
      monthlyReports: true,
    },
  },
});

export const createMockCategory = () => ({
  id: 'cat_1',
  name: 'Test Category',
  description: 'Test category description',
  icon: '🏠',
  color: '#FF6B6B',
  isDefault: false,
  userId: 'usr_1',
  expenseCount: 0,
  totalAmount: 0,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
});

export const createMockExpense = () => ({
  id: 'exp_1',
  amount: 25.5,
  description: 'Test expense',
  notes: null,
  date: '2024-01-15',
  paymentMethod: 'cash' as const,
  location: null,
  tags: [],
  categoryId: 'cat_1',
  userId: 'usr_1',
  receiptUrl: null,
  isRecurring: false,
  recurringFrequency: null,
  recurringEndDate: null,
  createdAt: '2024-01-15T14:30:00Z',
  updatedAt: '2024-01-15T14:30:00Z',
  category: createMockCategory(),
});

export const createMockApiResponse = <T>(data: T, success = true) => ({
  success,
  data,
  message: success ? 'Success' : 'Error',
});

export const createMockPaginatedResponse = <T>(data: T[], pagination = {}) => ({
  success: true,
  data,
  pagination: {
    page: 1,
    limit: 20,
    total: data.length,
    totalPages: 1,
    hasNext: false,
    hasPrev: false,
    ...pagination,
  },
});

// Wrapper pentru renderizarea componentelor cu provideri
// Duplicate imports removed - already imported at top
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return React.createElement(
    QueryClientProvider,
    { client: queryClient },
    React.createElement(BrowserRouter, null, children),
  );
};

const customRender: unknown = (ui: ReactElement, options?: Omit<RenderOptions, 'wrapper'>) =>
  render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };
