// Tipuri pentru API-ul backend
// Sincronizate cu backend-ul pentru consistență

// Tipuri de răspuns API
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: ValidationError[];
}

export interface PaginatedApiResponse<T = any> extends ApiResponse<T> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Tipuri pentru validare
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

// Tipuri pentru autentificare
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  currency?: string;
  timezone?: string;
}

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    currency: string;
    timezone: string;
    isActive: boolean;
    emailVerified: boolean;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

// Tipuri pentru utilizatori
export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  currency?: string;
  timezone?: string;
  avatar?: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// Tipuri pentru categorii
export interface CreateCategoryRequest {
  name: string;
  description?: string;
  color: string;
  icon: string;
  isDefault?: boolean;
  sortOrder?: number;
}

export interface UpdateCategoryRequest extends Partial<CreateCategoryRequest> {
  isActive?: boolean;
}

// Tipuri pentru cheltuieli
export interface CreateExpenseRequest {
  amount: number;
  description: string;
  date: string;
  categoryId: string;
  paymentMethod: 'cash' | 'card' | 'bank_transfer' | 'other';
  isRecurring?: boolean;
  recurringType?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  recurringEndDate?: string;
  tags?: string[];
  receipt?: string;
  location?: string;
  notes?: string;
}

export interface UpdateExpenseRequest extends Partial<CreateExpenseRequest> {}

// Tipuri pentru filtrare și căutare
export interface ExpenseFilters {
  startDate?: string;
  endDate?: string;
  categoryId?: string;
  paymentMethod?: string;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
  tags?: string[];
  isRecurring?: boolean;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Tipuri pentru export
export interface ExportRequest {
  format: 'csv' | 'excel' | 'pdf';
  startDate?: string;
  endDate?: string;
  categoryIds?: string[];
  includeCategories?: boolean;
  includeStats?: boolean;
}

// Tipuri pentru statistici
export interface StatsRequest {
  period: 'week' | 'month' | 'quarter' | 'year' | 'custom';
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month' | 'category';
  categoryIds?: string[];
}

export interface StatsResponse {
  totalExpenses: number;
  totalAmount: number;
  averageAmount: number;
  expenseCount: number;
  categoryBreakdown: Array<{
    categoryId: string;
    categoryName: string;
    amount: number;
    count: number;
    percentage: number;
  }>;
  timeSeriesData: Array<{
    date: string;
    amount: number;
    count: number;
  }>;
  paymentMethodBreakdown: Array<{
    method: string;
    amount: number;
    count: number;
    percentage: number;
  }>;
}

// Tipuri pentru abonamente
export interface SubscriptionRequest {
  priceId: string;
  paymentMethodId?: string;
}

export interface SubscriptionResponse {
  subscriptionId: string;
  clientSecret?: string;
  status: string;
  currentPeriodEnd: string;
}

// Tipuri pentru admin
export interface AdminUserFilters {
  search?: string;
  status?: 'active' | 'inactive';
  role?: 'user' | 'admin';
  subscriptionStatus?: 'active' | 'inactive' | 'canceled';
  registeredAfter?: string;
  registeredBefore?: string;
}

export interface AdminStatsResponse {
  totalUsers: number;
  activeUsers: number;
  totalExpenses: number;
  totalRevenue: number;
  subscriptionStats: {
    active: number;
    canceled: number;
    trial: number;
  };
  recentActivity: Array<{
    type: string;
    description: string;
    timestamp: string;
    userId?: string;
  }>;
}

// Tipuri pentru webhook-uri
export interface WebhookEvent {
  id: string;
  type: string;
  data: unknown;
  timestamp: string;
  processed: boolean;
}

// Tipuri pentru rate limiting
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

// Tipuri pentru erori API
export interface ApiError {
  code: string;
  message: string;
  details?: unknown;
  timestamp: string;
  path: string;
  method: string;
}

// Type guards pentru validare runtime
export function isApiResponse<T>(obj: unknown): obj is ApiResponse<T> {
  return obj && typeof obj === 'object' && typeof obj.success === 'boolean';
}

export function isPaginatedApiResponse<T>(obj: unknown): obj is PaginatedApiResponse<T> {
  return (
    isApiResponse(obj) &&
    'pagination' in obj &&
    Boolean(obj.pagination) &&
    typeof obj.pagination === 'object'
  );
}

export function isValidationError(obj: unknown): obj is ValidationError {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.field === 'string' &&
    typeof obj.message === 'string'
  );
}

export function isAuthResponse(obj: unknown): obj is AuthResponse {
  return (
    obj &&
    typeof obj === 'object' &&
    obj.user &&
    typeof obj.user === 'object' &&
    obj.tokens &&
    typeof obj.tokens === 'object' &&
    typeof obj.tokens.accessToken === 'string' &&
    typeof obj.tokens.refreshToken === 'string'
  );
}

export function isApiError(obj: unknown): obj is ApiError {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.code === 'string' &&
    typeof obj.message === 'string' &&
    typeof obj.timestamp === 'string'
  );
}

export function isWebhookEvent(obj: unknown): obj is WebhookEvent {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.type === 'string' &&
    typeof obj.processed === 'boolean' &&
    typeof obj.timestamp === 'string'
  );
}

// Constante pentru API
export const API_ENDPOINTS = {
  // Auth
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  REFRESH: '/auth/refresh',
  LOGOUT: '/auth/logout',
  VERIFY_EMAIL: '/auth/verify-email',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',

  // Users
  PROFILE: '/users/profile',
  UPDATE_PROFILE: '/users/profile',
  CHANGE_PASSWORD: '/users/change-password',
  DELETE_ACCOUNT: '/users/delete-account',
  USAGE_STATS: '/users/usage',

  // Categories
  CATEGORIES: '/categories',
  CATEGORY_BY_ID: (id: string) => `/categories/${id}`,

  // Expenses
  EXPENSES: '/expenses',
  EXPENSE_BY_ID: (id: string) => `/expenses/${id}`,
  EXPENSE_STATS: '/expenses/stats',

  // Export
  EXPORT: '/export',

  // Subscriptions
  SUBSCRIPTION: '/subscription',
  SUBSCRIPTION_PLANS: '/subscription/plans',
  SUBSCRIPTION_CANCEL: '/subscription/cancel',
  SUBSCRIPTION_RESUME: '/subscription/resume',

  // Admin
  ADMIN_USERS: '/admin/users',
  ADMIN_USER_BY_ID: (id: string) => `/admin/users/${id}`,
  ADMIN_STATS: '/admin/stats',
  ADMIN_SYSTEM_HEALTH: '/admin/system/health',
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// Content Types
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  URL_ENCODED: 'application/x-www-form-urlencoded',
  TEXT: 'text/plain',
  CSV: 'text/csv',
  EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  PDF: 'application/pdf',
} as const;
