import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';

// Mock pentru authService folosind vi.hoisted
const mockAuthService = vi.hoisted(() => ({
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  refreshToken: vi.fn(),
  getCurrentUser: vi.fn(),
  updateProfile: vi.fn(),
  changePassword: vi.fn(),
  clearAuthToken: vi.fn(),
  setAuthToken: vi.fn(),
}));

vi.mock('../../services/authService', () => ({
  authService: mockAuthService,
}));

import { useAuth, useAuthActions, useAuthStore } from '../../store/authStore';
import { createMockUser, createMockApiResponse } from '../../tests/setup';

// Mock pentru localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('useAuth Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    // Resetează starea store-ului
    useAuthStore.getState().clearAuth();
  });

  it('should initialize with default state', () => {
    // Arrange & Act
    const { result } = renderHook(() => useAuth());

    // Assert
    expect(result.current.user).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should initialize with stored user data', () => {
    // Arrange
    const mockUser = createMockUser();
    const authStorage = JSON.stringify({
      state: {
        user: mockUser,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
      version: 0,
    });
    mockLocalStorage.getItem.mockImplementation(key => {
      if (key === 'auth-storage') return authStorage;
      return null;
    });

    // Setează starea în store pentru a simula persistența
    act(() => {
      useAuthStore.getState().setUser(mockUser);
      useAuthStore.getState().setTokens('access-token', 'refresh-token');
    });

    // Act
    const { result } = renderHook(() => useAuth());

    // Assert
    expect(result.current.user).toEqual(mockUser);
    expect(result.current.isAuthenticated).toBe(true);
  });

  describe('login', () => {
    it('should login successfully', async () => {
      // Arrange
      const mockUser = createMockUser();
      const mockResponse = createMockApiResponse({
        user: mockUser,
        access_token: 'new-token',
        refresh_token: 'new-refresh-token',
      });

      mockAuthService.login.mockResolvedValue(mockResponse);

      const { result: authResult } = renderHook(() => useAuth());
      const { result: actionsResult } = renderHook(() => useAuthActions());

      // Act
      await act(async () => {
        await actionsResult.current.login({ email: '<EMAIL>', password: 'password123' });
      });

      // Assert
      expect(mockAuthService.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
      expect(authResult.current.user).toEqual(mockUser);
      expect(authResult.current.isAuthenticated).toBe(true);
      expect(authResult.current.error).toBeNull();
    });

    it('should handle login failure', async () => {
      // Arrange
      const errorResponse = {
        success: false,
        message: 'Invalid credentials',
      };

      mockAuthService.login.mockResolvedValue(errorResponse);

      const { result: authResult } = renderHook(() => useAuth());
      const { result: actionsResult } = renderHook(() => useAuthActions());

      // Act
      await act(async () => {
        await actionsResult.current.login({ email: '<EMAIL>', password: 'wrongpassword' });
      });

      // Assert
      expect(authResult.current.user).toBeNull();
      expect(authResult.current.isAuthenticated).toBe(false);
      expect(authResult.current.error).toBe('Invalid credentials');
    });

    it('should handle network errors during login', async () => {
      // Arrange
      mockAuthService.login.mockRejectedValue(new Error('Network error'));

      const { result: authResult } = renderHook(() => useAuth());
      const { result: actionsResult } = renderHook(() => useAuthActions());

      // Act
      await act(async () => {
        await actionsResult.current.login({ email: '<EMAIL>', password: 'password123' });
      });

      // Assert
      expect(authResult.current.user).toBeNull();
      expect(authResult.current.isAuthenticated).toBe(false);
      expect(authResult.current.error).toBe('Eroare la autentificare');
    });

    it('should set loading state during login', async () => {
      // Arrange
      let resolveLogin: (value: unknown) => void;
      const loginPromise = new Promise(resolve => {
        resolveLogin = resolve;
      });

      mockAuthService.login.mockReturnValue(loginPromise);

      const { result: authResult } = renderHook(() => useAuth());
      const { result: actionsResult } = renderHook(() => useAuthActions());

      // Act
      act(() => {
        actionsResult.current.login({ email: '<EMAIL>', password: 'password123' });
      });

      // Assert - loading state
      expect(authResult.current.isLoading).toBe(true);

      // Complete the login
      await act(async () => {
        resolveLogin!(
          createMockApiResponse({
            user: createMockUser(),
            access_token: 'token',
            refresh_token: 'refresh-token',
          }),
        );
        await loginPromise;
      });

      // Assert - loading complete
      expect(authResult.current.isLoading).toBe(false);
    });
  });

  describe('register', () => {
    it('should register successfully', async () => {
      // Arrange
      const registerData = {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        terms_accepted: true,
      };

      mockAuthService.register.mockResolvedValue({ success: true });

      const { result: authResult } = renderHook(() => useAuth());
      const { result: actionsResult } = renderHook(() => useAuthActions());

      // Act
      await act(async () => {
        const result = await actionsResult.current.register(registerData);
        expect(result.success).toBe(true);
      });

      // Assert
      expect(mockAuthService.register).toHaveBeenCalledWith(registerData);
      // Register nu setează utilizatorul - doar creează contul
      expect(authResult.current.user).toBeNull();
      expect(authResult.current.isAuthenticated).toBe(false);
      expect(authResult.current.isLoading).toBe(false);
    });

    it('should handle registration validation errors', async () => {
      // Arrange
      const errorResponse = {
        success: false,
        message: 'Validation failed',
        errors: {
          email: ['Email is already taken'],
          password: ['Password is too weak'],
        },
      };

      mockAuthService.register.mockResolvedValue(errorResponse);

      const { result: authResult } = renderHook(() => useAuth());
      const { result: actionsResult } = renderHook(() => useAuthActions());

      // Act
      await act(async () => {
        const result = await actionsResult.current.register({
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          password: 'weak',
          passwordConfirmation: 'weak',
          termsAccepted: true,
        });
        expect(result.success).toBe(false);
        expect(result.message).toBe('Validation failed');
      });

      // Assert - după register eșuat, utilizatorul rămâne null
      expect(authResult.current.user).toBeNull();
      expect(authResult.current.isAuthenticated).toBe(false);
      expect(authResult.current.error).toBe('Validation failed');
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      // Arrange
      const mockUser = createMockUser();
      mockAuthService.logout.mockResolvedValue({ success: true });

      const { result: authResult } = renderHook(() => useAuth());
      const { result: actionsResult } = renderHook(() => useAuthActions());

      // Set initial authenticated state using store
      act(() => {
        useAuthStore.getState().setUser(mockUser);
        useAuthStore.getState().setTokens('access-token', 'refresh-token');
      });

      // Act
      await act(async () => {
        await actionsResult.current.logout();
      });

      // Assert
      expect(mockAuthService.logout).toHaveBeenCalledWith('refresh-token');
      expect(authResult.current.user).toBeNull();
      expect(authResult.current.isAuthenticated).toBe(false);
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('auth-storage');
    });

    it('should clear local state even if logout API fails', async () => {
      // Arrange
      const mockUser = createMockUser();
      mockAuthService.logout.mockRejectedValue(new Error('Network error'));

      const { result: authResult } = renderHook(() => useAuth());
      const { result: actionsResult } = renderHook(() => useAuthActions());

      // Setează starea inițială autentificată
      act(() => {
        useAuthStore.getState().setUser(mockUser);
        useAuthStore.getState().setTokens('access-token', 'refresh-token');
      });

      // Act
      await act(async () => {
        await actionsResult.current.logout();
      });

      // Assert
      expect(authResult.current.user).toBeNull();
      expect(authResult.current.isAuthenticated).toBe(false);
      // Logout șterge starea locală chiar dacă API-ul eșuează
      expect(authResult.current.error).toBeNull();
    });
  });

  describe('updateProfile', () => {
    it('should update profile successfully', async () => {
      // Arrange
      const mockUser = createMockUser();
      const updatedData = { firstName: 'Updated', lastName: 'Name' };
      const mockResponse = createMockApiResponse(updatedData);

      mockAuthService.updateProfile.mockResolvedValue(mockResponse);

      const { result: authResult } = renderHook(() => useAuth());
      const { result: actionsResult } = renderHook(() => useAuthActions());

      // Setează utilizatorul inițial folosind store-ul direct
      act(() => {
        useAuthStore.getState().setUser(mockUser);
        useAuthStore.getState().setTokens('access-token', 'refresh-token');
      });

      // Act
      await act(async () => {
        await actionsResult.current.updateProfile(updatedData);
      });

      // Assert
      expect(mockAuthService.updateProfile).toHaveBeenCalledWith(updatedData);
      expect(authResult.current.user?.firstName).toBe('Updated');
      expect(authResult.current.user?.lastName).toBe('Name');
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      // Arrange
      const mockUser = createMockUser();
      const mockResponse = createMockApiResponse({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
      });

      mockAuthService.refreshToken.mockResolvedValue(mockResponse);

      const { result: authResult } = renderHook(() => useAuth());
      const { result: actionsResult } = renderHook(() => useAuthActions());

      // Setează starea inițială cu refresh token folosind store-ul direct
      act(() => {
        useAuthStore.getState().setUser(mockUser);
        useAuthStore.getState().setTokens('old-access-token', 'refresh-token');
      });

      // Act
      await act(async () => {
        await actionsResult.current.refreshAccessToken();
      });

      // Assert
      expect(mockAuthService.refreshToken).toHaveBeenCalledWith('refresh-token');
      expect(authResult.current.isAuthenticated).toBe(true);
    });

    it('should logout if refresh token is invalid', async () => {
      // Arrange
      mockLocalStorage.getItem.mockImplementation(key => {
        if (key === 'refresh_token') return 'invalid-token';
        return null;
      });

      mockAuthService.refreshToken.mockResolvedValue({
        success: false,
        message: 'Invalid refresh token',
      });

      const { result: authResult } = renderHook(() => useAuth());
      const { result: actionsResult } = renderHook(() => useAuthActions());

      // Act
      await act(async () => {
        await actionsResult.current.refreshAccessToken();
      });

      // Assert
      expect(authResult.current.user).toBeNull();
      expect(authResult.current.isAuthenticated).toBe(false);
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('auth-storage');
    });
  });
});
