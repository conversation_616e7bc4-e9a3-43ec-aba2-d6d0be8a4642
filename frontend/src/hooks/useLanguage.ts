import { toast } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';

import { changeLanguage, getCurrentLanguage, getSupportedLanguages } from '../i18n';
import { safeLog } from '../utils/safeLogger';

// Tipuri pentru hook-ul de limbă
interface LanguageOption {
  code: string;
  name: string;
}

interface UseLanguageReturn {
  t: (key: string, options?: unknown) => string;
  i18n: unknown;
  setLanguage: (language: string) => Promise<void>;
  currentLanguage: string;
  supportedLanguages: string[];
  isLanguageSupported: (language: string) => boolean;
  getLanguageDisplayName: (languageCode: string) => string;
  getLanguagesWithNames: () => LanguageOption[];
  isRTL: () => boolean;
}

/**
 * Hook personalizat pentru gestionarea limbii în aplicație
 */
export const useLanguage = (): UseLanguageReturn => {
  const { _t, _i18n } = useTranslation();

  /**
   * Schimbă limba aplicației
   */
  const setLanguage = async (language: string): Promise<void> => {
    try {
      await changeLanguage(language);

      // Afișează notificare de succes
      const successMessage = language === 'ro'
        ? 'Limba a fost schimbată cu succes!'
        : 'Language changed successfully!';

      toast.success(successMessage);
    } catch {
      safeLog.error('Error changing language:', error as Error);

      const errorMessage = getCurrentLanguage() === 'ro'
        ? 'Eroare la schimbarea limbii'
        : 'Error changing language';

      toast.error(errorMessage);
    }
  };

  /**
   * Obține limba curentă
   */
  const currentLanguage = getCurrentLanguage();

  /**
   * Obține lista limbilor suportate
   */
  const supportedLanguages = getSupportedLanguages();

  /**
   * Verifică dacă o limbă este suportată
   */
  const isLanguageSupported = (language: string): boolean => {
    return supportedLanguages.includes(language);
  };

  /**
   * Obține numele afișat al unei limbi
   */
  const getLanguageDisplayName = (languageCode: string): string => {
    return t(`languages.${languageCode}`, { defaultValue: languageCode.toUpperCase() });
  };

  /**
   * Obține toate limbile cu numele lor afișat
   */
  const getLanguagesWithNames = (): LanguageOption[] => {
    return supportedLanguages.map(code => ({
      code,
      name: getLanguageDisplayName(code),
    }));
  };

  /**
   * Verifică dacă aplicația este în modul RTL (Right-to-Left)
   */
  const isRTL = (): boolean => {
    // Adaugă aici limbile RTL dacă sunt suportate în viitor
    const rtlLanguages = ['ar', 'he', 'fa'];
    return rtlLanguages.includes(currentLanguage);
  };

  return {
    // Funcția de traducere
    t,

    // Obiectul i18n pentru acces direct
    i18n,

    // Funcții pentru gestionarea limbii
    setLanguage,
    currentLanguage,
    supportedLanguages,
    isLanguageSupported,
    getLanguageDisplayName,
    getLanguagesWithNames,
    isRTL,
  };
};

export default useLanguage;
