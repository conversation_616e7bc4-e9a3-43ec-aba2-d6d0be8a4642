{"name": "expense-tracker-frontend", "private": true, "version": "1.0.0", "description": "Frontend pentru aplicația de urmărire a cheltuielilor", "scripts": {"dev": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" webpack serve --mode development", "build": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" tsc --noEmit && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" webpack --mode production", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix", "preview": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" webpack serve --mode production", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,css,md}\"", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "analyze": "npm run build", "clean": "rm -rf dist", "security:audit": "npm audit", "security:fix": "npm audit fix", "security:check": "npm audit --audit-level moderate", "prepare": "husky install", "pre-commit": "lint-staged", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "lighthouse": "lhci autorun", "lighthouse:desktop": "node ../scripts/lighthouse-test.js desktop", "lighthouse:mobile": "node ../scripts/lighthouse-test.js mobile", "lighthouse:production": "node ../scripts/lighthouse-test.js production", "lighthouse:ci": "node ../scripts/lighthouse-test.js ci", "lighthouse:all": "node ../scripts/lighthouse-test.js all", "perf:audit": "npm run lighthouse:all", "perf:report": "lhci upload"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "chart.js": "^4.5.0", "clsx": "^2.0.0", "currency.js": "^2.0.4", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "js-cookie": "^3.0.5", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^4.24.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-i18next": "^15.6.0", "react-router-dom": "^6.20.1", "react-select": "^5.8.0", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@eslint/js": "^9.30.1", "@tanstack/react-query-devtools": "^5.81.5", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/js-cookie": "^3.0.6", "@types/react": "^18.3.23", "@types/react-datepicker": "^4.19.6", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^0.34.6", "@vitest/ui": "^0.34.6", "autoprefixer": "^10.4.16", "babel-loader": "^10.0.0", "compression-webpack-plugin": "^11.1.0", "copy-webpack-plugin": "^13.0.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "eslint": "^8.53.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "eslint-plugin-security": "^3.0.1", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "image-webpack-loader": "^8.1.0", "jsdom": "^23.0.1", "lint-staged": "^16.1.2", "postcss": "^8.4.32", "postcss-loader": "^8.1.1", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "style-loader": "^4.0.0", "tailwindcss": "^3.3.6", "terser-webpack-plugin": "^5.3.14", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "vitest": "^0.34.6", "webpack": "^5.99.9", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2", "workbox-background-sync": "^7.3.0", "workbox-core": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "workbox-webpack-plugin": "^7.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,md,json}": ["prettier --write"]}}